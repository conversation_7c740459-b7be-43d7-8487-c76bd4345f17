{"name": "js-confuser", "version": "2.0.0", "description": "JavaScript Obfuscation Tool.", "main": "dist/index.js", "types": "index.d.ts", "scripts": {"build": "tsc && babel src --out-dir dist --extensions \".ts,.tsx\"", "dev": "node babel.register.js", "test": "jest --force<PERSON>xit", "test:coverage": "jest --coverage --coverageReporters=html", "prepublishOnly": "npm run test && npm run build"}, "keywords": ["obfuscator", "obfuscation", "uglify", "code protection", "javascript obfuscator", "js obfuscator"], "author": "MichaelXF", "license": "MIT", "dependencies": {"@babel/generator": "^7.25.6", "@babel/parser": "^7.25.6", "@babel/traverse": "^7.25.6", "@babel/types": "^7.25.6", "lz-string": "^1.5.0"}, "devDependencies": {"@babel/cli": "^7.24.8", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/preset-env": "^7.25.3", "@babel/preset-typescript": "^7.24.7", "@babel/register": "^7.24.6", "@eslint/js": "^9.9.0", "@types/babel__core": "^7.20.5", "@types/jest": "^29.5.12", "@types/node": "^22.2.0", "globals": "^15.9.0", "jest": "^29.7.0", "typescript": "^5.5.4", "typescript-eslint": "^8.1.0"}, "repository": {"type": "git", "url": "https://github.com/MichaelXF/js-confuser"}, "bugs": {"url": "https://github.com/MichaelXF/js-confuser/issues"}, "homepage": "https://js-confuser.com", "engines": {"node": ">=18.0.0"}}