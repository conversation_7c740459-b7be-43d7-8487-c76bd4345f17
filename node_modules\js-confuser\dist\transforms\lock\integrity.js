"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = exports.INTEGRITY = void 0;
var _order = require("../../order");
var _randomUtils = require("../../utils/random-utils");
var _integrityTemplate = require("../../templates/integrityTemplate");
var t = _interopRequireWildcard(require("@babel/types"));
var _template = _interopRequireDefault(require("../../templates/template"));
var _NameGen = require("../../utils/NameGen");
function _interopRequireDefault(e) { return e && e.__esModule ? e : { "default": e }; }
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { "default": e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n["default"] = e, t && t.set(e, n), n; }
var INTEGRITY = exports.INTEGRITY = Symbol("Integrity");
/**
 * Integrity has two passes:
 *
 * - First in the 'lock' plugin to select functions and prepare them for Integrity
 * - Secondly here to apply the integrity check
 *
 * This transformation must run last as any changes to the code will break the hash
 */
var _default = exports["default"] = function _default(_ref) {
  var Plugin = _ref.Plugin;
  var me = Plugin(_order.Order.Integrity, {
    changeData: {
      functions: 0
    }
  });
  var nameGen = new _NameGen.NameGen(me.options.identifierGenerator, {
    avoidObjectPrototype: true,
    avoidReserved: true
  });
  return {
    visitor: {
      Program: {
        enter: function enter(path) {
          path.scope.crawl();
        }
      },
      FunctionDeclaration: {
        exit: function exit(funcDecPath) {
          var integrityInterface = funcDecPath.node[INTEGRITY];
          if (!integrityInterface) return;
          var newFnPath = integrityInterface.fnPath;
          if (newFnPath.removed) return;
          var newFunctionDeclaration = newFnPath.node;
          if (!newFunctionDeclaration || !t.isFunctionDeclaration(newFunctionDeclaration)) return;
          var hashFnName = me.globalState.internals.integrityHashName;
          var obfuscatedHashFnName = me.obfuscator.getObfuscatedVariableName(hashFnName, funcDecPath.find(function (p) {
            return p.isProgram();
          }).node);
          var newFnName = newFunctionDeclaration.id.name;
          var binding = newFnPath.scope.getBinding(newFnName);

          // Function is redefined, do not apply integrity
          if (!binding || binding.constantViolations.length > 0) return;
          var code = me.obfuscator.generateCode(newFunctionDeclaration);
          var codeTrimmed = code.replace(me.globalState.lock.integrity.sensitivityRegex, "");
          var seed = (0, _randomUtils.getRandomInteger)(0, 10000000);
          var hashCode = (0, _integrityTemplate.HashFunction)(codeTrimmed, seed);
          var selfName = funcDecPath.node.id.name;
          var selfCacheProperty = nameGen.generate();
          var selfCacheString = "".concat(selfName, ".").concat(selfCacheProperty);

          // me.log(codeTrimmed, hashCode);
          me.changeData.functions++;
          var hashName = nameGen.generate();
          funcDecPath.node.body = t.blockStatement(new _template["default"]("\n              var {hashName} = ".concat(selfCacheString, " || (").concat(selfCacheString, " = ").concat(obfuscatedHashFnName, "(").concat(newFunctionDeclaration.id.name, ", ").concat(seed, "));\n          if({hashName} === ").concat(hashCode, ") {\n            {originalBody}\n          } else {\n            {countermeasures}  \n          }\n          ")).compile({
            originalBody: funcDecPath.node.body.body,
            hashName: hashName,
            countermeasures: function countermeasures() {
              return me.globalState.lock.createCountermeasuresCode();
            }
          }),
          // Preserve directives
          funcDecPath.node.body.directives);
        }
      }
    }
  };
};