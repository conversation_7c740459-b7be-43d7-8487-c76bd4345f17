"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _order = require("../../order");
var t = _interopRequireWildcard(require("@babel/types"));
var _astUtils = require("../../utils/ast-utils");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { "default": e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n["default"] = e, t && t.set(e, n), n; }
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
var _default = exports["default"] = function _default(_ref) {
  var Plugin = _ref.Plugin;
  var me = Plugin(_order.Order.ObjectExtraction, {
    changeData: {
      objects: 0
    }
  });
  return {
    visitor: {
      Program: {
        enter: function enter(path) {
          path.scope.crawl();
        }
      },
      VariableDeclaration: function VariableDeclaration(varDecPath) {
        if (varDecPath.node.declarations.length !== 1) return;
        var declaration = varDecPath.get("declarations.0");

        // Must be simple variable declaration (No destructuring)
        var identifier = declaration.get("id");
        if (!identifier.isIdentifier()) return;

        // Must be an object expression
        var objectExpression = declaration.get("init");
        if (!objectExpression.isObjectExpression()) return;

        // Not allowed to reassign the object
        var binding = varDecPath.scope.getBinding(identifier.node.name);
        if (!binding || binding.constantViolations.length > 0) return;
        var pendingReplacements = [];
        var newObjectName = me.getPlaceholder() + "_" + identifier.node.name;
        var newPropertyMappings = new Map();

        // Create new property names from the original object properties
        var newDeclarations = [];
        var _iterator = _createForOfIteratorHelper(objectExpression.get("properties")),
          _step;
        try {
          for (_iterator.s(); !(_step = _iterator.n()).done;) {
            var property = _step.value;
            if (!property.isObjectProperty()) return;
            var propertyKey = (0, _astUtils.getObjectPropertyAsString)(property.node);
            if (!propertyKey) {
              // Property key is not a static string, not allowed
              return;
            }
            var newPropertyName = newPropertyMappings.get(propertyKey);
            if (newPropertyName) {
              // Duplicate property, not allowed
              return;
            } else {
              newPropertyName = newObjectName + "_" + (t.isValidIdentifier(propertyKey) ? propertyKey : me.getPlaceholder());
              newPropertyMappings.set(propertyKey, newPropertyName);
            }

            // Check function for referencing 'this'
            var value = property.get("value");
            if (value.isFunction()) {
              var referencesThis = false;
              value.traverse({
                ThisExpression: function ThisExpression(thisPath) {
                  referencesThis = true;
                }
              });
              if (referencesThis) {
                // Function references 'this', not allowed
                // When extracted, this will not refer to the original object
                return;
              }
            }
            newDeclarations.push(t.variableDeclarator(t.identifier(newPropertyName), value.node));
          }
        } catch (err) {
          _iterator.e(err);
        } finally {
          _iterator.f();
        }
        var isObjectSafe = true;
        (0, _astUtils.getParentFunctionOrProgram)(varDecPath).traverse({
          Identifier: {
            exit: function exit(idPath) {
              if (idPath.node.name !== identifier.node.name) return;
              if (idPath === identifier) return; // Skip the original declaration

              var memberExpression = idPath.parentPath;
              if (!memberExpression || !memberExpression.isMemberExpression()) {
                isObjectSafe = false;
                return;
              }
              var property = (0, _astUtils.getMemberExpressionPropertyAsString)(memberExpression.node);
              if (!property) {
                isObjectSafe = false;
                return;
              }

              // Delete expression check
              if (memberExpression.parentPath.isUnaryExpression({
                operator: "delete"
              })) {
                // Deleting object properties is not allowed
                isObjectSafe = false;
                return;
              }
              var newPropertyName = newPropertyMappings.get(property);
              if (!newPropertyName) {
                // Property added later on, not allowed
                isObjectSafe = false;
                return;
              }
              var extractedIdentifier = t.identifier(newPropertyName);
              pendingReplacements.push({
                path: memberExpression,
                replaceWith: extractedIdentifier
              });
            }
          }
        });

        // Object references are too complex to safely extract
        if (!isObjectSafe) return;
        if (!me.computeProbabilityMap(me.options.objectExtraction, identifier.node.name)) return;
        var newDeclarationKind = varDecPath.node.kind === "const" ? "let" : varDecPath.node.kind;
        varDecPath.replaceWithMultiple(newDeclarations.map(function (declaration) {
          return t.variableDeclaration(newDeclarationKind, [declaration]);
        })).forEach(function (path) {
          // Make sure to register the new declarations
          path.scope.registerDeclaration(path);
        });

        // Replace all references to new singular identifiers
        for (var _i = 0, _pendingReplacements = pendingReplacements; _i < _pendingReplacements.length; _i++) {
          var _pendingReplacements$ = _pendingReplacements[_i],
            path = _pendingReplacements$.path,
            replaceWith = _pendingReplacements$.replaceWith;
          path.replaceWith(replaceWith);
        }
        me.log("Extracted object", identifier.node.name);
        me.changeData.objects++;
      }
    }
  };
};