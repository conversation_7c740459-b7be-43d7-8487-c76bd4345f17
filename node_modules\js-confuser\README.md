# JS Confuser

JS-Confuser is a JavaScript obfuscation tool to make your programs _impossible_ to read. [Try the web version](https://js-confuser.com).

  [![NPM](https://img.shields.io/badge/NPM-%23000000.svg?style=for-the-badge&logo=npm&logoColor=white)](https://npmjs.com/package/js-confuser) [![GitHub](https://img.shields.io/badge/github-%23121011.svg?style=for-the-badge&logo=github&logoColor=white)](https://github.com/MichaelXF/js-confuser) [![Netlify](https://img.shields.io/badge/netlify-%23000000.svg?style=for-the-badge&logo=netlify&logoColor=#00C7B7)](https://js-confuser.com)

## Key features

- Variable renaming
- Control Flow obfuscation
- String concealing
- Function obfuscation
- Locks (domainLock, date)
- [Detect changes to source code](https://new--confuser.netlify.app/docs/options/integrity#integrity)

## Documentation

Get started in the [`JS-Confuser Docs`](https://js-confuser.com/docs/).

- [Getting Started](https://js-confuser.com/docs)

- - [What is Obfuscation?](https://js-confuser.com/docs/getting-started/what-is-obfuscation)

- - [Playground](https://js-confuser.com/docs/getting-started/playground)

- - [Installation](https://js-confuser.com/docs/getting-started/installation)

- - [Usage](https://js-confuser.com/docs/getting-started/usage)

- - [FAQ](https://js-confuser.com/docs/getting-started/faq)

- [Options](https://js-confuser.com/docs/options)

- [Presets](https://js-confuser.com/docs/presets)

## API Usage

### Installation

```bash
$ npm install js-confuser
```

### Usage

```js
var JsConfuser = require("js-confuser");

JsConfuser.obfuscate(`
  function fibonacci(num){   
    var a = 0, b = 1, c = num;
    while (num-- > 1) {
      c = a + b;
      a = b;
      b = c;
    }
    return c;
  }

  for ( var i = 1; i <= 25; i++ ) {
    console.log(i, fibonacci(i))
  }
`, {
  target: "node",
  preset: "high",
  stringEncoding: false, // <- Normally enabled
}).then(result => {
  console.log(result.code)
})

/*
Function("tZzJOP","var iSDaP3,MyrZnn,PugOol,zlitc1Y,NqvmoV,w01dEg,PEGf2Ir,wQdUIp,YwPLty,BNOmOCh,CHDWybv,MsWp2Eg,q5kdT5,tDyAgBo,Nmc7b1;const WydP2H=[\"length\",0x2,0x0,0x1,\"c\",0x3e,null,0x20,\"a\",0x100,0x6,0x3,0x8,0x10,0x4,\"undefined\",\"LZString\",0x25,\"h\",0xe2,0x8d,0x34,0x5,\"f\",0xf5,0xff,0x7,0xd,0xe,0xf,0x58,0x5b,0x4f,0xf1,0x31,0xc1,0x80,void 0x0,\"d\",\"b\",0x7f,0x1f,0x3f,0xc,0xc7,0x12,0xbd,0xc9,0x9d,0x48,\"i\",\"g\",0x1fff,0x77,0x15,0x84,0x24,0x9,\"e\",0x63,0x64,0x22,0xaa,0xfa,0x65,0x66,0x67,0x68,0x69,0xcf,0x6a,0x6b,0xe9,0xf2,0xd4,0xd8,0x6c,0x6d,0x28,0xa,0x8a,0x6e,0x6f,0x70,0x71,0x72,0x73,0x74,0x75,0x76,0x78,0x79,0x7a,0x7b,0x7c,0x7d,0x7e,0x81,0x82,0x83,0x85,0x86,0x87,0x88,0x89,0x8b,0x8c,0x8e,0x8f,0x90,0x91,\"8\",0x92,0x93,0x94,0x95,0x96,0x97,0x98,0x99,0x9a,0x9b,0x9c,0x9e,0x9f,0xa0,0xa1,0xa2,0xa3,0xa4,0xa5,0xa6,0xa7,0xa8,0xa9,0xab,0xac,0xad,0xae,0xaf,0xc4,0x40,0xed,0xb0,0xe8,0xb1,0xb2,0xb3,0xb4,0xbf,0xb6,0xb5,0xb7,0xea,0xf6,0xb8,0x53,0xb9,\"t\",!0x1,0xba,0xbb,0xbc,0xbe,0xc0,0xc2,0xc3,0xc5,0xc6,0x4d,0xc8,0xca,0xcb,0xcc,0xcd,0x19];Fj4SwuY(YVrVbv(be5E4W),YVrVbv(g7PNjlU),YVrVbv(Hxpsdj_),YVrVbv(putCzy));function YVrVbv(MyrZnn,PugOol=WydP2H[0x3]){Object.defineProperty(MyrZnn,WydP2H[0x0],{value:PugOol,configurable:WydP2H[0x9f]});return MyrZnn}Fj4SwuY(iSDaP3=function(...MyrZnn){Fj4SwuY(MyrZnn[WydP2H[0x0]]=WydP2H[0x2],YVrVbv(PEGf2Ir,WydP2H[0x1]));var PugOol=String.fromCharCode,zlitc1Y=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\",NqvmoV=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$\",w01dEg={};function PEGf2Ir(...MyrZnn){var PugOol,zlitc1Y;function*NqvmoV(zlitc1Y,NqvmoV,PEGf2Ir,wQdUIp,YwPLty={r78IiA:{}}){while(zlitc1Y+NqvmoV+PEGf2Ir+wQdUIp!==0x1e)with(YwPLty.Vohu0Q||YwPLty)switch(zlitc1Y+NqvmoV+PEGf2Ir+wQdUIp){case 0x5f:case 0xef:Fj4SwuY(YwPLty.r78IiA.FpCHxeU=-0x4,MyrZnn[WydP2H[0x0]]=WydP2H[zlitc1Y+0x10]);if(!w01dEg[MyrZnn[WydP2H[0x2]]]){Fj4SwuY(YwPLty.Vohu0Q=YwPLty.r78IiA,zlitc1Y+=-0xb1,NqvmoV+=0x7b,PEGf2Ir+=-0x4c,wQdUIp+=0x4a);break}else{Fj4SwuY(YwPLty.Vohu0Q=YwPLty.r78IiA,zlitc1Y+=-0x28,NqvmoV+=0x11b,PEGf2Ir+=-0x152,wQdUIp+=0x4a);break}case YwPLty.r78IiA.FpCHxeU+0x4e:return PugOol=!0x0,w01dEg[MyrZnn[WydP2H[0x2]]][MyrZnn[WydP2H[PEGf2Ir+0xda]]];case-0x93:case 0x27:case 0x2a:w01dEg[MyrZnn[WydP2H[zlitc1Y+0xc2]]]={};for(MyrZnn[WydP2H[zlitc1Y+0xc1]]=WydP2H[0x2];MyrZnn[WydP2H[0x1]]<MyrZnn[WydP2H[0x2]].length;MyrZnn[WydP2H[PEGf2Ir+-0x2e]]++)w01dEg[MyrZnn[WydP2H[zlitc1Y+0xc2]]][MyrZnn[WydP2H[PEGf2Ir+-0x2d]].charAt(MyrZnn[WydP2H[0x1]])]=MyrZnn[WydP2H[NqvmoV+-0x5b]];Fj4SwuY(YwPLty.Vohu0Q=YwPLty.r78IiA,zlitc1Y+=0x89,NqvmoV+=0xa0,PEGf2Ir+=-0x106);break;case YwPLty.r78IiA.FpCHxeU+0x14:Fj4SwuY(YwPLty.Vohu0Q=YwPLty.r78IiA,zlitc1Y+=-0xd2,NqvmoV+=0x169,PEGf2Ir+=-0x15d,wQdUIp+=0x100);break;case NqvmoV- -0xff:Fj4SwuY(YwPLty.Vohu0Q=YwPLty.IjdvHE,zlitc1Y+=-0x6e,NqvmoV+=0x1c,PEGf2Ir+=-0xeb,wQdUIp+=0xd8);break;default:Fj4SwuY(YwPLty.Vohu0Q=YwPLty.UUeBR2,zlitc1Y+=-0x1e,NqvmoV+=-0xc6,PEGf2Ir+=0xca,wQdUIp+=-0x6b);break;case YwPLty.r78IiA.FpCHxeU+0x64:Fj4SwuY(YwPLty.Vohu0Q=YwPLty.dcYkgZ6,zlitc1Y+=-0x6e,NqvmoV+=0x44,PEGf2Ir+=-0x66,wQdUIp+=0x8f);break;case YwPLty.r78IiA.FpCHxeU+0x10:Fj4SwuY(YwPLty.r78IiA.FpCHxeU=0x1c,YwPLty.Vohu0Q=YwPLty.r78IiA,zlitc1Y+=-0xbe,NqvmoV+=0x169,PEGf2Ir+=-0x15d,wQdUIp+=0xf0);break}}Fj4SwuY(PugOol=void 0x0,zlitc1Y=NqvmoV(-0xf,-0x1f,0x7b,0x12).next().value);if(PugOol){return zlitc1Y}}MyrZnn[WydP2H[0x4]]={compressToBase64:YVrVbv(function(...PugOol){var NqvmoV,w01dEg;function*PEGf2Ir(w01dEg,PEGf2Ir,wQdUIp,YwPLty={UeCEL4_:{}}){while(w01dEg+PEGf2Ir+wQdUIp!==-0x8a)with(YwPLty.GpihL4o||YwPLty)switch(w01dEg+PEGf2Ir+wQdUIp){case YwPLty.UeCEL4_.uWtqTC+-0xe6:PugOol[WydP2H[0x5]]=MyrZnn[WydP2H[0x4]]._compress(PugOol[WydP2H[w01dEg+-0x36]],WydP2H[0xa],YVrVbv(function(...w01dEg){w01dEg[WydP2H[0x0]]=WydP2H[0x3];return zlitc1Y.charAt(w01dEg[WydP2H[0x2]])}));switch(PugOol[WydP2H[0x5]].length%WydP2H[0xe]){default:case WydP2H[0x2]:return NqvmoV=!0x0,PugOol[WydP2H[0x5]];case WydP2H[0x3]:return NqvmoV=!0x0,PugOol[WydP2H[0x5]]+\"===\";case WydP2H[0x1]:return NqvmoV=!0x0,PugOol[WydP2H[w01dEg+-0x33]]+\"==\";case WydP2H[w01dEg+-0x2d]:return NqvmoV=!0x0,PugOol[WydP2H[PEGf2Ir+-0x60]]+\"=\"}Fj4SwuY(YwPLty.GpihL4o=YwPLty.g4B1IWG,PEGf2Ir+=-0xfd,wQdUIp+=0xae);break;case 0xac:case 0x37:Fj4SwuY(YwPLty.GpihL4o=YwPLty.UeCEL4_,w01dEg+=-0x3e,PEGf2Ir+=-0xeb,wQdUIp+=0xe);break;case 0x1c:case 0xf4:Fj4SwuY([YwPLty.UeCEL4_.aKWU7u,YwPLty.UeCEL4_.uWtqTC,YwPLty.UeCEL4_.nF1ngYQ]=[-0xa4,0xab,-0x5],PugOol[WydP2H[PEGf2Ir+-0x49]]=WydP2H[0x3]);if(WydP2H[PEGf2Ir+-0x43]==PugOol[WydP2H[PEGf2Ir+-0x47]])return NqvmoV=!0x0,\"\";Fj4SwuY(YwPLty.GpihL4o=YwPLty.UeCEL4_,w01dEg+=-0x45,PEGf2Ir+=0x1c,wQdUIp+=-0x2e);break;case w01dEg-0x10f:case-0x5:case-0x1e:Fj4SwuY(YwPLty.GpihL4o=YwPLty.UeCEL4_,w01dEg+=-0x68,PEGf2Ir+=0x85,wQdUIp+=0x17);break;default:Fj4SwuY([YwPLty.UeCEL4_.aKWU7u,YwPLty.UeCEL4_.uWtqTC,YwPLty.UeCEL4_.nF1ngYQ]=[-0x54,0xf5,-0x19],YwPLty.GpihL4o=YwPLty.pMZ3Cz,w01dEg+=0x84,PEGf2Ir+=0x9,wQdUIp+=-0x126);break;case w01dEg- -0x26:Fj4SwuY(YwPLty.GpihL4o=YwPLty.TbtugV,w01dEg+=0x9a,PEGf2Ir+=0x69,wQdUIp+=-0xf0);break;case 0x91:case 0x0:case YwPLty.UeCEL4_.nF1ngYQ+-0x55:Fj4SwuY(YwPLty.GpihL4o=YwPLty.oBcZJzn,w01dEg+=-0x62,PEGf2Ir+=0x1e3,wQdUIp+=-0x10b);break}}Fj4SwuY(NqvmoV=void 0x0,w01dEg=PEGf2Ir(0x7d,0x49,-0xaa).next().value);if(NqvmoV){return w01dEg}}),decompressFromBase64:YVrVbv(function(...PugOol){PugOol[WydP2H[0x0]]=WydP2H[0x3];return WydP2H[0x6]==PugOol[WydP2H[0x2]]?\"\":\"\"==PugOol[WydP2H[0x2]]?WydP2H[0x6]:MyrZnn[WydP2H[0x4]]._decompress(PugOol[WydP2H[0x2]].length,WydP2H[0x7],function(MyrZnn){return PEGf2Ir(zlitc1Y,PugOol[WydP2H[0x2]].charAt(MyrZnn))})}),compressToUTF16:function(NqvmoV){return WydP2H[0x6]==NqvmoV?\"\":MyrZnn[WydP2H[0x4]]._compress(NqvmoV,WydP2H[0x1d],YVrVbv(function(...NqvmoV){NqvmoV[WydP2H[0x0]]=WydP2H[0x3];return PugOol(NqvmoV[WydP2H[0x2]]+WydP2H[0x7])}))+\" \"},decompressFromUTF16:YVrVbv(function(...PugOol){PugOol[WydP2H[0x0]]=WydP2H[0x3];return WydP2H[0x6]==PugOol[WydP2H[0x2]]?\"\":\"\"==PugOol[WydP2H[0x2]]?WydP2H[0x6]:MyrZnn[WydP2H[0x4]]._decompress(PugOol[WydP2H[0x2]].length,0x4000,YVrVbv(function(...MyrZnn){MyrZnn[WydP2H[0x0]]=WydP2H[0x3];return PugOol[WydP2H[0x2]].charCodeAt(MyrZnn[WydP2H[0x2]])-WydP2H[0x7]}))}),compressToUint8Array:YVrVbv(function(...PugOol){PugOol[WydP2H[0x0]]=WydP2H[0x3];for(var wQdUIp=MyrZnn[WydP2H[0x4]].compress(PugOol[WydP2H[0x2]]),YwPLty=new Uint8Array(WydP2H[0x1]*wQdUIp.length),BNOmOCh=WydP2H[0x2],CHDWybv=wQdUIp.length;BNOmOCh<CHDWybv;BNOmOCh++){Fj4SwuY(PugOol[WydP2H[0x8]]=wQdUIp.charCodeAt(BNOmOCh),YwPLty[WydP2H[0x1]*BNOmOCh]=PugOol[WydP2H[0x8]]>>>WydP2H[0xc],YwPLty[WydP2H[0x1]*BNOmOCh+WydP2H[0x3]]=PugOol[WydP2H[0x8]]%WydP2H[0x9])}return YwPLty}),decompressFromUint8Array:YVrVbv(function(...zlitc1Y){zlitc1Y[WydP2H[0x0]]=WydP2H[0x3];if(WydP2H[0x6]==zlitc1Y[WydP2H[0x2]])return MyrZnn[WydP2H[0x4]].decompress(zlitc1Y[WydP2H[0x2]]);for(var wQdUIp=new Array(zlitc1Y[WydP2H[0x2]].length/WydP2H[0x1]),YwPLty=WydP2H[0x2],BNOmOCh=wQdUIp.length;YwPLty<BNOmOCh;YwPLty++)wQdUIp[YwPLty]=WydP2H[0x9]*zlitc1Y[WydP2H[0x2]][WydP2H[0x1]*YwPLty]+zlitc1Y[WydP2H[0x2]][WydP2H[0x1]*YwPLty+WydP2H[0x3]];zlitc1Y[WydP2H[0x3]]=[];return wQdUIp.forEach(YVrVbv(function(...wQdUIp){Fj4SwuY(wQdUIp[WydP2H[0x0]]=WydP2H[0x3],zlitc1Y[WydP2H[0x3]].push(PugOol(wQdUIp[WydP2H[0x2]])))})),MyrZnn[WydP2H[0x4]].decompress(zlitc1Y[WydP2H[0x3]].join(\"\"))}),compressToEncodedURIComponent:YVrVbv(function(...PugOol){PugOol[WydP2H[0x0]]=WydP2H[0x3];return WydP2H[0x6]==PugOol[WydP2H[0x2]]?\"\":MyrZnn[WydP2H[0x4]]._compress(PugOol[WydP2H[0x2]],WydP2H[0xa],YVrVbv(function(...PugOol){PugOol[WydP2H[0x0]]=WydP2H[0x3];return NqvmoV.charAt(PugOol[WydP2H[0x2]])}))}),decompressFromEncodedURIComponent:YVrVbv(function(...PugOol){PugOol[WydP2H[0x0]]=WydP2H[0x3];return WydP2H[0x6]==PugOol[WydP2H[0x2]]?\"\":\"\"==PugOol[WydP2H[0x2]]?WydP2H[0x6]:(PugOol[WydP2H[0x2]]=PugOol[WydP2H[0x2]].replace(/ /g,\"+\"),MyrZnn[WydP2H[0x4]]._decompress(PugOol[WydP2H[0x2]].length,WydP2H[0x7],YVrVbv(function(...MyrZnn){MyrZnn[WydP2H[0x0]]=WydP2H[0x3];return PEGf2Ir(NqvmoV,PugOol[WydP2H[0x2]].charAt(MyrZnn[WydP2H[0x2]]))})))}),compress:function(NqvmoV){return MyrZnn[WydP2H[0x4]]._compress(NqvmoV,WydP2H[0xd],function(MyrZnn){return PugOol(MyrZnn)})},_compress:function(MyrZnn,wQdUIp,YwPLty){if(WydP2H[0x6]==MyrZnn)return\"\";var BNOmOCh,CHDWybv,MsWp2Eg,q5kdT5={},tDyAgBo={},YVrVbv=\"\",putCzy=\"\",Hxpsdj_=\"\",UkVIMy=WydP2H[0x1],g7PNjlU=WydP2H[0xb],RT3qJW_=WydP2H[0x1],qLPG1Tw=[],be5E4W=WydP2H[0x2],GpA0Rz=WydP2H[0x2];for(MsWp2Eg=WydP2H[0x2];MsWp2Eg<MyrZnn.length;MsWp2Eg+=WydP2H[0x3])if(YVrVbv=MyrZnn.charAt(MsWp2Eg),Object.prototype.hasOwnProperty.call(q5kdT5,YVrVbv)||(q5kdT5[YVrVbv]=g7PNjlU++,tDyAgBo[YVrVbv]=!WydP2H[0x2]),putCzy=Hxpsdj_+YVrVbv,Object.prototype.hasOwnProperty.call(q5kdT5,putCzy))Hxpsdj_=putCzy;else{if(Object.prototype.hasOwnProperty.call(tDyAgBo,Hxpsdj_)){if(Hxpsdj_.charCodeAt(WydP2H[0x2])<WydP2H[0x9]){for(BNOmOCh=WydP2H[0x2];BNOmOCh<RT3qJW_;BNOmOCh++)be5E4W<<=WydP2H[0x3],GpA0Rz==wQdUIp-WydP2H[0x3]?(GpA0Rz=WydP2H[0x2],qLPG1Tw.push(YwPLty(be5E4W)),be5E4W=WydP2H[0x2]):GpA0Rz++;for(CHDWybv=Hxpsdj_.charCodeAt(WydP2H[0x2]),BNOmOCh=WydP2H[0x2];BNOmOCh<WydP2H[0xc];BNOmOCh++)be5E4W=be5E4W<<WydP2H[0x3]|WydP2H[0x3]&CHDWybv,GpA0Rz==wQdUIp-WydP2H[0x3]?(GpA0Rz=WydP2H[0x2],qLPG1Tw.push(YwPLty(be5E4W)),be5E4W=WydP2H[0x2]):GpA0Rz++,CHDWybv>>=WydP2H[0x3]}else{for(CHDWybv=WydP2H[0x3],BNOmOCh=WydP2H[0x2];BNOmOCh<RT3qJW_;BNOmOCh++)be5E4W=be5E4W<<WydP2H[0x3]|CHDWybv,GpA0Rz==wQdUIp-WydP2H[0x3]?(GpA0Rz=WydP2H[0x2],qLPG1Tw.push(YwPLty(be5E4W)),be5E4W=WydP2H[0x2]):GpA0Rz++,CHDWybv=WydP2H[0x2];for(CHDWybv=Hxpsdj_.charCodeAt(WydP2H[0x2]),BNOmOCh=WydP2H[0x2];BNOmOCh<WydP2H[0xd];BNOmOCh++)be5E4W=be5E4W<<WydP2H[0x3]|WydP2H[0x3]&CHDWybv,GpA0Rz==wQdUIp-WydP2H[0x3]?(GpA0Rz=WydP2H[0x2],qLPG1Tw.push(YwPLty(be5E4W)),be5E4W=WydP2H[0x2]):GpA0Rz++,CHDWybv>>=WydP2H[0x3]}Fj4SwuY(WydP2H[0x2]==--UkVIMy&&(UkVIMy=Math.pow(WydP2H[0x1],RT3qJW_),RT3qJW_++),delete tDyAgBo[Hxpsdj_])}else for(CHDWybv=q5kdT5[Hxpsdj_],BNOmOCh=WydP2H[0x2];BNOmOCh<RT3qJW_;BNOmOCh++)be5E4W=be5E4W<<WydP2H[0x3]|WydP2H[0x3]&CHDWybv,GpA0Rz==wQdUIp-WydP2H[0x3]?(GpA0Rz=WydP2H[0x2],qLPG1Tw.push(YwPLty(be5E4W)),be5E4W=WydP2H[0x2]):GpA0Rz++,CHDWybv>>=WydP2H[0x3];Fj4SwuY(WydP2H[0x2]==--UkVIMy&&(UkVIMy=Math.pow(WydP2H[0x1],RT3qJW_),RT3qJW_++),q5kdT5[putCzy]=g7PNjlU++,Hxpsdj_=String(YVrVbv))}if(\"\"!==Hxpsdj_){if(Object.prototype.hasOwnProperty.call(tDyAgBo,Hxpsdj_)){if(Hxpsdj_.charCodeAt(WydP2H[0x2])<WydP2H[0x9]){for(BNOmOCh=WydP2H[0x2];BNOmOCh<RT3qJW_;BNOmOCh++)be5E4W<<=WydP2H[0x3],GpA0Rz==wQdUIp-WydP2H[0x3]?(GpA0Rz=WydP2H[0x2],qLPG1Tw.push(YwPLty(be5E4W)),be5E4W=WydP2H[0x2]):GpA0Rz++;for(CHDWybv=Hxpsdj_.charCodeAt(WydP2H[0x2]),BNOmOCh=WydP2H[0x2];BNOmOCh<WydP2H[0xc];BNOmOCh++)be5E4W=be5E4W<<WydP2H[0x3]|WydP2H[0x3]&CHDWybv,GpA0Rz==wQdUIp-WydP2H[0x3]?(GpA0Rz=WydP2H[0x2],qLPG1Tw.push(YwPLty(be5E4W)),be5E4W=WydP2H[0x2]):GpA0Rz++,CHDWybv>>=WydP2H[0x3]}else{for(CHDWybv=WydP2H[0x3],BNOmOCh=WydP2H[0x2];BNOmOCh<RT3qJW_;BNOmOCh++)be5E4W=be5E4W<<WydP2H[0x3]|CHDWybv,GpA0Rz==wQdUIp-WydP2H[0x3]?(GpA0Rz=WydP2H[0x2],qLPG1Tw.push(YwPLty(be5E4W)),be5E4W=WydP2H[0x2]):GpA0Rz++,CHDWybv=WydP2H[0x2];for(CHDWybv=Hxpsdj_.charCodeAt(WydP2H[0x2]),BNOmOCh=WydP2H[0x2];BNOmOCh<WydP2H[0xd];BNOmOCh++)be5E4W=be5E4W<<WydP2H[0x3]|WydP2H[0x3]&CHDWybv,GpA0Rz==wQdUIp-WydP2H[0x3]?(GpA0Rz=WydP2H[0x2],qLPG1Tw.push(YwPLty(be5E4W)),be5E4W=WydP2H[0x2]):GpA0Rz++,CHDWybv>>=WydP2H[0x3]}Fj4SwuY(WydP2H[0x2]==--UkVIMy&&(UkVIMy=Math.pow(WydP2H[0x1],RT3qJW_),RT3qJW_++),delete tDyAgBo[Hxpsdj_])}else for(CHDWybv=q5kdT5[Hxpsdj_],BNOmOCh=WydP2H[0x2];BNOmOCh<RT3qJW_;BNOmOCh++)be5E4W=be5E4W<<WydP2H[0x3]|WydP2H[0x3]&CHDWybv,GpA0Rz==wQdUIp-WydP2H[0x3]?(GpA0Rz=WydP2H[0x2],qLPG1Tw.push(YwPLty(be5E4W)),be5E4W=WydP2H[0x2]):GpA0Rz++,CHDWybv>>=WydP2H[0x3];WydP2H[0x2]==--UkVIMy&&(UkVIMy=Math.pow(WydP2H[0x1],RT3qJW_),RT3qJW_++)}for(CHDWybv=WydP2H[0x1],BNOmOCh=WydP2H[0x2];BNOmOCh<RT3qJW_;BNOmOCh++)be5E4W=be5E4W<<WydP2H[0x3]|WydP2H[0x3]&CHDWybv,GpA0Rz==wQdUIp-WydP2H[0x3]?(GpA0Rz=WydP2H[0x2],qLPG1Tw.push(YwPLty(be5E4W)),be5E4W=WydP2H[0x2]):GpA0Rz++,CHDWybv>>=WydP2H[0x3];for(;;){if(be5E4W<<=WydP2H[0x3],GpA0Rz==wQdUIp-WydP2H[0x3]){qLPG1Tw.push(YwPLty(be5E4W));break}GpA0Rz++}return qLPG1Tw.join(\"\")},decompress:YVrVbv(function(...PugOol){PugOol[WydP2H[0x0]]=WydP2H[0x3];return WydP2H[0x6]==PugOol[WydP2H[0x2]]?\"\":\"\"==PugOol[WydP2H[0x2]]?WydP2H[0x6]:MyrZnn[WydP2H[0x4]]._decompress(PugOol[WydP2H[0x2]].length,0x8000,YVrVbv(function(...MyrZnn){MyrZnn[WydP2H[0x0]]=WydP2H[0x3];return PugOol[WydP2H[0x2]].charCodeAt(MyrZnn[WydP2H[0x2]])}))}),_decompress:function(MyrZnn,wQdUIp,YwPLty){var BNOmOCh,CHDWybv,MsWp2Eg,q5kdT5,tDyAgBo,YVrVbv,putCzy,Hxpsdj_=[],UkVIMy=WydP2H[0xe],g7PNjlU=WydP2H[0xe],RT3qJW_=WydP2H[0xb],qLPG1Tw=\"\",be5E4W=[],GpA0Rz={val:YwPLty(WydP2H[0x2]),position:wQdUIp,index:WydP2H[0x3]};for(BNOmOCh=WydP2H[0x2];BNOmOCh<WydP2H[0xb];BNOmOCh+=WydP2H[0x3])Hxpsdj_[BNOmOCh]=BNOmOCh;for(MsWp2Eg=WydP2H[0x2],tDyAgBo=Math.pow(WydP2H[0x1],WydP2H[0x1]),YVrVbv=WydP2H[0x3];YVrVbv!=tDyAgBo;)q5kdT5=GpA0Rz.val&GpA0Rz.position,GpA0Rz.position>>=WydP2H[0x3],WydP2H[0x2]==GpA0Rz.position&&(GpA0Rz.position=wQdUIp,GpA0Rz.val=YwPLty(GpA0Rz.index++)),MsWp2Eg|=(q5kdT5>WydP2H[0x2]?WydP2H[0x3]:WydP2H[0x2])*YVrVbv,YVrVbv<<=WydP2H[0x3];switch(MsWp2Eg){case WydP2H[0x2]:for(MsWp2Eg=WydP2H[0x2],tDyAgBo=Math.pow(WydP2H[0x1],WydP2H[0xc]),YVrVbv=WydP2H[0x3];YVrVbv!=tDyAgBo;)q5kdT5=GpA0Rz.val&GpA0Rz.position,GpA0Rz.position>>=WydP2H[0x3],WydP2H[0x2]==GpA0Rz.position&&(GpA0Rz.position=wQdUIp,GpA0Rz.val=YwPLty(GpA0Rz.index++)),MsWp2Eg|=(q5kdT5>WydP2H[0x2]?WydP2H[0x3]:WydP2H[0x2])*YVrVbv,YVrVbv<<=WydP2H[0x3];putCzy=PugOol(MsWp2Eg);break;case WydP2H[0x3]:for(MsWp2Eg=WydP2H[0x2],tDyAgBo=Math.pow(WydP2H[0x1],WydP2H[0xd]),YVrVbv=WydP2H[0x3];YVrVbv!=tDyAgBo;)q5kdT5=GpA0Rz.val&GpA0Rz.position,GpA0Rz.position>>=WydP2H[0x3],WydP2H[0x2]==GpA0Rz.position&&(GpA0Rz.position=wQdUIp,GpA0Rz.val=YwPLty(GpA0Rz.index++)),MsWp2Eg|=(q5kdT5>WydP2H[0x2]?WydP2H[0x3]:WydP2H[0x2])*YVrVbv,YVrVbv<<=WydP2H[0x3];putCzy=PugOol(MsWp2Eg);break;case WydP2H[0x1]:return\"\"}for(Hxpsdj_[WydP2H[0xb]]=putCzy,CHDWybv=putCzy,be5E4W.push(putCzy);;){if(GpA0Rz.index>MyrZnn)return\"\";for(MsWp2Eg=WydP2H[0x2],tDyAgBo=Math.pow(WydP2H[0x1],RT3qJW_),YVrVbv=WydP2H[0x3];YVrVbv!=tDyAgBo;)q5kdT5=GpA0Rz.val&GpA0Rz.position,GpA0Rz.position>>=WydP2H[0x3],WydP2H[0x2]==GpA0Rz.position&&(GpA0Rz.position=wQdUIp,GpA0Rz.val=YwPLty(GpA0Rz.index++)),MsWp2Eg|=(q5kdT5>WydP2H[0x2]?WydP2H[0x3]:WydP2H[0x2])*YVrVbv,YVrVbv<<=WydP2H[0x3];switch(putCzy=MsWp2Eg){case WydP2H[0x2]:for(MsWp2Eg=WydP2H[0x2],tDyAgBo=Math.pow(WydP2H[0x1],WydP2H[0xc]),YVrVbv=WydP2H[0x3];YVrVbv!=tDyAgBo;)q5kdT5=GpA0Rz.val&GpA0Rz.position,GpA0Rz.position>>=WydP2H[0x3],WydP2H[0x2]==GpA0Rz.position&&(GpA0Rz.position=wQdUIp,GpA0Rz.val=YwPLty(GpA0Rz.index++)),MsWp2Eg|=(q5kdT5>WydP2H[0x2]?WydP2H[0x3]:WydP2H[0x2])*YVrVbv,YVrVbv<<=WydP2H[0x3];Fj4SwuY(Hxpsdj_[g7PNjlU++]=PugOol(MsWp2Eg),putCzy=g7PNjlU-WydP2H[0x3],UkVIMy--);break;case WydP2H[0x3]:for(MsWp2Eg=WydP2H[0x2],tDyAgBo=Math.pow(WydP2H[0x1],WydP2H[0xd]),YVrVbv=WydP2H[0x3];YVrVbv!=tDyAgBo;)q5kdT5=GpA0Rz.val&GpA0Rz.position,GpA0Rz.position>>=WydP2H[0x3],WydP2H[0x2]==GpA0Rz.position&&(GpA0Rz.position=wQdUIp,GpA0Rz.val=YwPLty(GpA0Rz.index++)),MsWp2Eg|=(q5kdT5>WydP2H[0x2]?WydP2H[0x3]:WydP2H[0x2])*YVrVbv,YVrVbv<<=WydP2H[0x3];Fj4SwuY(Hxpsdj_[g7PNjlU++]=PugOol(MsWp2Eg),putCzy=g7PNjlU-WydP2H[0x3],UkVIMy--);break;case WydP2H[0x1]:return be5E4W.join(\"\")}if(WydP2H[0x2]==UkVIMy&&(UkVIMy=Math.pow(WydP2H[0x1],RT3qJW_),RT3qJW_++),Hxpsdj_[putCzy])qLPG1Tw=Hxpsdj_[putCzy];else{if(putCzy!==g7PNjlU)return WydP2H[0x6];qLPG1Tw=CHDWybv+CHDWybv.charAt(WydP2H[0x2])}Fj4SwuY(be5E4W.push(qLPG1Tw),Hxpsdj_[g7PNjlU++]=CHDWybv+qLPG1Tw.charAt(WydP2H[0x2]),CHDWybv=qLPG1Tw,WydP2H[0x2]==--UkVIMy&&(UkVIMy=Math.pow(WydP2H[0x1],RT3qJW_),RT3qJW_++))}}};return MyrZnn[WydP2H[0x4]]}(),\"function\"==tZzJOP[\"TE0C7CR\"]&&tZzJOP[\"_8iE0h\"].amd?tZzJOP[\"_8iE0h\"](function(){return iSDaP3}):WydP2H[0xf]!=tZzJOP[\"soSW4YC\"]&&WydP2H[0x6]!=tZzJOP[\"pXH_Js\"]?tZzJOP[\"pXH_Js\"].exports=iSDaP3:WydP2H[0xf]!=tZzJOP[\"xOGz1y\"]&&WydP2H[0x6]!=tZzJOP[\"ZQZggA\"]&&tZzJOP[\"ZQZggA\"].module(WydP2H[0x10],[]).factory(WydP2H[0x10],function(){return iSDaP3}),MyrZnn=void 0x0,function(){var PugOol=\"\\u15E1\\u6C29\\u416C\\u0680\\u4B30\\u5823\\u703F<\\u0298\\u0460\\u3CB4\\u03C0\\u0440\\u1EA0\\u04A0\\u1621\\u4E21\\u75F0\\u024D\\u592A\\u0238\\u0800\\u0CC2\\u404Bf\\u09D8\\u08E0\\u0E80\\u0BE0\\u0800\\u0D69\\u447C\\u205B\\u09B4e\\u147A\\xC2|\\u198C\\xC4h8\\u01A4\\u0154\\xDC\\u018D\\u4820\\u5C84\\u0824\\u72A8\\u1026\\u602C\\u202A\\u2021\\u202B\\u11F6\\u6027\\u4031\\u602A\\u6023\\u0821\\u1821\\u52A1\\u4433\\u0EA0\\u0660\\u02C6\\u3024\\u52C04\\u5420\\u4980\\u4032\\u42A0\\u0103\\u5292\\u1823\\u0C20\\u02E8\\u6748]\\u53B3\\u68AE\\u0EE0\\u0409\\u7515\\u422A\\u0520\\u1CB6\\u40A3\\u1468\\u7027\\u0961\\u4421\\u6E20\\u1B55\\u1273\\u1C78\\u29EF\\u7180\\xF0\\u3B31\\u7E11\\u3220\\u375F\\u4CE2\\u4320\\u2635\\u7BC0\\xF4\\xF6\\u62F8\\u7031\\u43DF\\u46E7\\u4946\\u0821\\u4894t\\u61B2\\u3698\\u5820\\u0CE1\\u22CD\\u1019\\u12F4\\u6620\\u17E9\\u4043\\u1CEE\\u3FAF\\u0170\\xA26\\u402D\\u57B9\\u6E97\\u25AA\\u4022\\u5570\\u58C2\\u04B0\\u11A6\\u3D4B\\u4A45\\u21A2\\u25A9\\u4346\\u3890\\u61BC\\u1A36\\u71A6\\u5466\\u2020\\u1A55\\u5880\\u1FC2\\u2319\\u7323\\u0F01\\u232C\\u1064\\u1485 \\u6F13#\\u6FE0\\u0128\\u1967\\u13E3\\u04E2\\u4443\\u4CAB\\u0A58\\u0764\\u1BA2\\u225C\\u0E5C\\u5248\\u4161\\u2932\\u106A\\u6255\\u4E8B\\u0DA1\\u509D\\u618C\\u4064\\u22D5\\u683D\\u1049\\u73A8\\u1994\\u0A48\\u7820\\u1AF8\\u3661\\u2379\\u08E1\\u3627\\u6539\\u0E20\\u37CD\\u1002\\u1D14%\\u5548\\u1254\\u4AF4\\u5FBE\\u7411\\u4E80\\u0223\\u02C4\\u2C94\\u17E3\\u0588\\u42D0\\u1C49\\u6021\\u6A2A\\u0CA6\\u0BC6\\u5027\\u0930\\u0A37\\u79F8\\u1337\\u1FA9\\u74BC\\u25BB\\u1461\\u4CC3\\u1272\\u6172\\u6CDE\\u25A8\\u4322\\u7CFC\\u373F\\u61A5\\u40E3\\u0EBB\\u43E8\\u54A8\\u2738\\u3150\\u2099\\u3CE7\\u2528\\u48C8\\u1D5B\\u01A7\\u108A\\u052B\\u2548\\u289A\\u3626\\u2729\\u56C9\\u2228\\u6501\\u20B8\\u127F\\u4806\\u3C98\\u3BA3\\u7696\\u62C5\\u1137\\u190A\\u7C7A\\u20BE\\u15D2\\u0180\\u2C67\\u2352\\u30B6\\u11A6\\u0BAE\\u5167\\u22C2\\u4B23\\u1254\\u24AA\\u092E\\u7E31\\u0B20\\u472A\\u41C8\\u232F\\u035A\\u62BD\\u0258\\u573B\\u4038\\u03A0\\u42C2\\u513B\\u02E4\\u5439\\u6BD8\\u4124\\u40D0\\u5A38\\u0150\\u23AE\\u0950 ...
*/
```


## Bug report

Please [open an issue](https://github.com/MichaelXF/js-confuser/issues) with the code and config used.

## Feature request

Please [open an issue](https://github.com/MichaelXF/js-confuser/issues) and be descriptive. Don't submit any PRs until approved.

## License

MIT License