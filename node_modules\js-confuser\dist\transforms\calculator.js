"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var t = _interopRequireWildcard(require("@babel/types"));
var _order = require("../order");
var _assert = require("assert");
var _NameGen = require("../utils/NameGen");
var _astUtils = require("../utils/ast-utils");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { "default": e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n["default"] = e, t && t.set(e, n), n; }
function _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t["return"] && (u = t["return"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }
function _arrayWithHoles(r) { if (Array.isArray(r)) return r; }
var _default = exports["default"] = function _default(_ref) {
  var Plugin = _ref.Plugin;
  var me = Plugin(_order.Order.Calculator, {
    changeData: {
      expressions: 0
    }
  });
  var nameGen = new _NameGen.NameGen(me.options.identifierGenerator);
  return {
    visitor: {
      Program: {
        exit: function exit(programPath) {
          var allowedBinaryOperators = new Set(["+", "-", "*", "/"]);
          var operatorsMap = new Map();
          var calculatorFnName = me.getPlaceholder() + "_calc";
          programPath.traverse({
            BinaryExpression: {
              exit: function exit(path) {
                var operator = path.node.operator;
                if (t.isPrivate(path.node.left)) return;

                // TODO: Improve precedence handling or remove this transformation entirely
                if (!t.isNumericLiteral(path.node.right)) return;
                if (!t.isNumericLiteral(path.node.left)) return;
                if (!allowedBinaryOperators.has(operator)) return;
                var mapKey = "binaryExpression_" + operator;
                var operatorKey = operatorsMap.get(mapKey);

                // Add binary operator to the map if it doesn't exist
                if (typeof operatorKey === "undefined") {
                  operatorKey = nameGen.generate();
                  operatorsMap.set(mapKey, operatorKey);
                }
                (0, _assert.ok)(operatorKey);
                me.changeData.expressions++;
                path.replaceWith(t.callExpression(t.identifier(calculatorFnName), [t.stringLiteral(operatorKey), path.node.left, path.node.right]));
              }
            }
          });

          // No operators created
          if (operatorsMap.size < 1) {
            return;
          }

          // Create the calculator function and insert into program path
          var switchCases = Array.from(operatorsMap.entries()).map(function (_ref2) {
            var _ref3 = _slicedToArray(_ref2, 2),
              mapKey = _ref3[0],
              key = _ref3[1];
            var _mapKey$split = mapKey.split("_"),
              _mapKey$split2 = _slicedToArray(_mapKey$split, 2),
              type = _mapKey$split2[0],
              operator = _mapKey$split2[1];
            var expression = t.binaryExpression(operator, t.identifier("a"), t.identifier("b"));
            return t.switchCase(t.stringLiteral(key), [t.returnStatement(expression)]);
          });
          (0, _astUtils.prependProgram)(programPath, t.functionDeclaration(t.identifier(calculatorFnName), [t.identifier("operator"), t.identifier("a"), t.identifier("b")], t.blockStatement([t.switchStatement(t.identifier("operator"), switchCases)])));
        }
      }
    }
  };
};