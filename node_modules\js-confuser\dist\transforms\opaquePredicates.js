"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _order = require("../order");
var t = _interopRequireWildcard(require("@babel/types"));
var _randomUtils = require("../utils/random-utils");
var _PredicateGen = _interopRequireDefault(require("../utils/PredicateGen"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { "default": e }; }
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { "default": e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n["default"] = e, t && t.set(e, n), n; }
var _default = exports["default"] = function _default(_ref) {
  var Plugin = _ref.Plugin;
  var me = Plugin(_order.Order.OpaquePredicates, {
    changeData: {
      opaquePredicates: 0
    }
  });
  var predicateGen = new _PredicateGen["default"](me);
  function createTruePredicate(path) {
    return predicateGen.generateTrueExpression(path);
  }
  var active = true;
  var transformCount = 0;
  function shouldTransform(path) {
    if (!active) return false;
    if (path.find(function (p) {
      return me.isSkipped(p);
    })) return false;
    if (!me.computeProbabilityMap(me.options.opaquePredicates)) return false;
    transformCount++;
    var depth = path.getAncestry().length;
    return (0, _randomUtils.chance)(500 - transformCount - depth * 100);
  }
  function wrapWithPredicate(path) {
    var newExpression = t.logicalExpression("&&", createTruePredicate(path), path.node);
    me.changeData.opaquePredicates++;
    path.replaceWith(me.skip(newExpression));
  }
  return {
    visitor: {
      // if (test) -> if (PREDICATE() && test) {}
      IfStatement: {
        exit: function exit(path) {
          if (!shouldTransform(path)) return;
          wrapWithPredicate(path.get("test"));
        }
      },
      // test ? a : b -> PREDICATE() && test ? a : b
      ConditionalExpression: {
        exit: function exit(path) {
          if (!shouldTransform(path)) return;
          wrapWithPredicate(path.get("test"));
        }
      },
      // case test: -> case PREDICATE() && test:
      SwitchCase: {
        exit: function exit(path) {
          if (!path.node.test) return;
          if (!shouldTransform(path)) return;
          wrapWithPredicate(path.get("test"));
        }
      },
      // return test -> if (predicate()) { return test } else { return fake }
      ReturnStatement: {
        exit: function exit(path) {
          if (!path.node.argument) return;
          if (!shouldTransform(path)) return;
          me.changeData.opaquePredicates++;
          path.replaceWith(t.ifStatement(createTruePredicate(path), t.blockStatement([t.returnStatement(path.node.argument)]), t.blockStatement([t.returnStatement(t.stringLiteral((0, _randomUtils.getRandomString)(6)))])));
          me.skip(path);
        }
      }
    }
  };
};