"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Order = void 0;
/**
 * Describes the order of transformations.
 */
var Order = exports.Order = /*#__PURE__*/function (Order) {
  Order[Order["Preparation"] = 0] = "Preparation";
  Order[Order["ObjectExtraction"] = 1] = "ObjectExtraction";
  Order[Order["Flatten"] = 2] = "Flatten";
  Order[Order["Lock"] = 3] = "Lock";
  Order[Order["RGF"] = 4] = "RGF";
  Order[Order["Dispatcher"] = 6] = "Dispatcher";
  Order[Order["DeadCode"] = 8] = "DeadCode";
  Order[Order["Calculator"] = 9] = "Calculator";
  Order[Order["GlobalConcealing"] = 12] = "GlobalConcealing";
  Order[Order["OpaquePredicates"] = 13] = "OpaquePredicates";
  Order[Order["StringSplitting"] = 16] = "StringSplitting";
  Order[Order["StringConcealing"] = 17] = "StringConcealing";
  Order[Order["StringCompression"] = 18] = "StringCompression";
  Order[Order["VariableMasking"] = 20] = "VariableMasking";
  Order[Order["DuplicateLiteralsRemoval"] = 22] = "DuplicateLiteralsRemoval";
  Order[Order["Shuffle"] = 23] = "Shuffle";
  Order[Order["ControlFlowFlattening"] = 24] = "ControlFlowFlattening";
  Order[Order["MovedDeclarations"] = 25] = "MovedDeclarations";
  Order[Order["RenameLabels"] = 27] = "RenameLabels";
  Order[Order["Minify"] = 28] = "Minify";
  Order[Order["AstScrambler"] = 29] = "AstScrambler";
  Order[Order["RenameVariables"] = 30] = "RenameVariables";
  Order[Order["Finalizer"] = 35] = "Finalizer";
  Order[Order["Pack"] = 36] = "Pack";
  Order[Order["Integrity"] = 37] = "Integrity";
  return Order;
}({}); // Must run last