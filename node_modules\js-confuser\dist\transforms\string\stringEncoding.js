"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var t = _interopRequireWildcard(require("@babel/types"));
var _randomUtils = require("../../utils/random-utils");
var _constants = require("../../constants");
var _astUtils = require("../../utils/ast-utils");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { "default": e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n["default"] = e, t && t.set(e, n), n; }
function pad(x, len) {
  while (x.length < len) {
    x = "0" + x;
  }
  return x;
}
function even(x) {
  if (x.length % 2 != 0) {
    return "0" + x;
  }
  return x;
}
function toHexRepresentation(str) {
  var escapedString = "";
  str.split("").forEach(function (_char) {
    var code = _char.charCodeAt(0);
    if (code < 128) {
      escapedString += "\\x" + even(pad(code.toString(16), 2));
    } else {
      escapedString += _char;
    }
  });
  return escapedString;
}
function toUnicodeRepresentation(str) {
  var escapedString = "";
  str.split("").forEach(function (_char2) {
    var code = _char2.charCodeAt(0);
    if (code < 128) {
      escapedString += "\\u" + even(pad(code.toString(16), 4));
    } else {
      escapedString += _char2;
    }
  });
  return escapedString;
}
var _default = exports["default"] = function _default(me) {
  return {
    visitor: {
      StringLiteral: {
        exit: function exit(path) {
          // Ignore module imports
          if ((0, _astUtils.isModuleImport)(path)) return;
          var value = path.node.value;

          // Allow percentages
          if (!me.computeProbabilityMap(me.options.stringEncoding, value)) return;
          var type = (0, _randomUtils.choice)(["hexadecimal", "unicode"]);
          var escapedString = (type == "hexadecimal" ? toHexRepresentation : toUnicodeRepresentation)(value);
          var id = t.identifier("\"".concat(escapedString, "\""));
          id[_constants.GEN_NODE] = true;
          path.replaceWith(id);
          path.skip();
        }
      }
    }
  };
};