"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.chance = chance;
exports.choice = choice;
exports.getRandom = getRandom;
exports.getRandomChineseString = getRandomChineseString;
exports.getRandomHexString = getRandomHexString;
exports.getRandomInteger = getRandomInteger;
exports.getRandomString = getRandomString;
exports.shuffle = shuffle;
exports.splitIntoChunks = splitIntoChunks;
var _assert = require("assert");
function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _iterableToArray(r) { if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r); }
function _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
/**
 * Returns a random element from the given array
 * @param choices Array of items
 * @returns One of the items in the array at random
 */
function choice(choices) {
  var index = Math.floor(Math.random() * choices.length);
  return choices[index];
}

/**
 * Returns a true/false based on the percent chance (0%-100%)
 * @param percentChance AS A PERCENTAGE 0 - 100%
 */
function chance(percentChance) {
  return Math.random() < percentChance / 100;
}

/**
 * **Mutates the given array**
 * @param array
 */
function shuffle(array) {
  array.sort(function () {
    return Math.random() - 0.5;
  });
  return array;
}

/**
 * Returns a random hexadecimal string.
 *
 * @example getRandomHexString(6) => "CA96BF"
 * @param length
 * @returns
 */
function getRandomHexString(length) {
  return _toConsumableArray(Array(length)).map(function () {
    return Math.floor(Math.random() * 16).toString(16);
  }).join("").toUpperCase();
}

/**
 * @see https://github.com/MichaelXF/js-confuser/issues/150#issuecomment-2466159582
 */
function getRandomChineseString(length) {
  var characters = [];
  for (var i = 0; i < length; i++) characters.push(String.fromCharCode(Math.floor(Math.random() * (0x9fff - 0x4e00)) + 0x4e00));
  return characters.join("");
}

/**
 * Returns a random string.
 */
function getRandomString(length) {
  var result = "";
  var characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  var charactersLength = characters.length;
  for (var i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}
function getRandom(min, max) {
  return Math.random() * (max - min) + min;
}
function getRandomInteger(min, max) {
  return Math.floor(getRandom(min, max));
}
function splitIntoChunks(str, size) {
  (0, _assert.ok)(typeof str === "string", "str must be typeof string");
  (0, _assert.ok)(typeof size === "number", "size must be typeof number");
  (0, _assert.ok)(Math.floor(size) === size, "size must be integer");
  var numChunks = Math.ceil(str.length / size);
  var chunks = new Array(numChunks);
  for (var i = 0, o = 0; i < numChunks; ++i, o += size) {
    chunks[i] = str.substr(o, size);
  }
  return chunks;
}