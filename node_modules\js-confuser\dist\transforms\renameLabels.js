"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = _default;
var t = _interopRequireWildcard(require("@babel/types"));
var _order = require("../order");
var _NameGen = require("../utils/NameGen");
var _assert = require("assert");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { "default": e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n["default"] = e, t && t.set(e, n), n; }
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
var LABEL = Symbol("label");
function _default(_ref) {
  var Plugin = _ref.Plugin;
  var me = Plugin(_order.Order.RenameLabels, {
    changeData: {
      labelsRenamed: 0,
      labelsRemoved: 0
    }
  });
  return {
    visitor: {
      Program: function Program(path) {
        var allLabelInterfaces = [];

        // First pass: Collect all label usages
        path.traverse({
          LabeledStatement: function LabeledStatement(labelPath) {
            var labelInterface = {
              label: labelPath.node.label.name,
              removed: false,
              required: false,
              paths: []
            };
            allLabelInterfaces.push(labelInterface);
            labelPath.node[LABEL] = labelInterface;
          },
          "BreakStatement|ContinueStatement": function BreakStatementContinueStatement(_path) {
            var path = _path;
            if (path.node.label) {
              var labelName = path.node.label.name;
              var targets = [];
              var onlySearchLoops = path.isContinueStatement();
              var currentPath = path;
              while (currentPath) {
                if (currentPath.isFor() || currentPath.isWhile() || currentPath.isSwitchStatement()) {
                  targets.push(currentPath);
                }
                if (currentPath.isBlockStatement() && currentPath.parentPath.isLabeledStatement()) {
                  targets.push(currentPath);
                }
                currentPath = currentPath.parentPath;
              }
              var target = targets.find(function (label) {
                return label.parentPath && label.parentPath.isLabeledStatement() && label.parentPath.node.label.name === labelName;
              });
              if (onlySearchLoops) {
                // Remove BlockStatements and SwitchStatements from the list of targets
                // a continue statement only target loops
                // This helps remove unnecessary labels when a continue is nested with a block statement
                // ex: for-loop with if-statement continue
                targets = targets.filter(function (target) {
                  return !target.isBlockStatement() && !target.isSwitchStatement();
                });
              }
              (0, _assert.ok)(target);
              var isRequired = target.isBlockStatement() || targets[0] !== target;
              var _labelInterface = target.parentPath.node[LABEL];
              if (isRequired) {
                _labelInterface.required = true;
              } else {
                // Label is not required here, remove it for this particular break/continue statement
                path.node.label = null;
              }
              if (!_labelInterface.paths) {
                _labelInterface.paths = [];
              }
              _labelInterface.paths.push(path);
            }
          }
        });
        var nameGen = new _NameGen.NameGen(me.options.identifierGenerator);
        for (var _i = 0, _allLabelInterfaces = allLabelInterfaces; _i < _allLabelInterfaces.length; _i++) {
          var labelInterface = _allLabelInterfaces[_i];
          var isRequired = labelInterface.required;
          if (isRequired) {
            var newName = labelInterface.label;
            if (me.computeProbabilityMap(me.options.renameLabels, labelInterface.label)) {
              newName = nameGen.generate();
            }
            labelInterface.renamed = newName;
            me.changeData.labelsRenamed++;
          } else {
            labelInterface.removed = true;
            me.changeData.labelsRemoved++;
          }
        }

        // Second pass: Rename labels and remove unused ones
        path.traverse({
          LabeledStatement: function LabeledStatement(labelPath) {
            var labelInterface = labelPath.node[LABEL];
            if (labelInterface) {
              // Remove label but replace it with its body
              if (labelInterface.removed) {
                labelPath.replaceWith(labelPath.node.body);
              }

              // Else keep the label but rename it
              if (typeof labelInterface.renamed === "string") {
                labelPath.node.label.name = labelInterface.renamed;
              }

              // Update all break/continue statements
              var _iterator = _createForOfIteratorHelper(labelInterface.paths),
                _step;
              try {
                for (_iterator.s(); !(_step = _iterator.n()).done;) {
                  var breakPath = _step.value;
                  // Remove label from break/continue statement
                  if (labelInterface.removed) {
                    breakPath.node.label = null;
                  } else {
                    // Update label name
                    breakPath.node.label = t.identifier(labelInterface.renamed);
                  }
                }
              } catch (err) {
                _iterator.e(err);
              } finally {
                _iterator.f();
              }
            }
          }
        });
      }
    }
  };
}