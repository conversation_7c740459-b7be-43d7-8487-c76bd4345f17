"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var babelTypes = _interopRequireWildcard(require("@babel/types"));
var _parser = require("@babel/parser");
var _traverse = _interopRequireDefault(require("@babel/traverse"));
var _assert = require("assert");
var _randomUtils = require("../utils/random-utils");
function _interopRequireDefault(e) { return e && e.__esModule ? e : { "default": e }; }
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { "default": e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n["default"] = e, t && t.set(e, n), n; }
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
// Create a union type of the symbol keys in NodeSymbol
var Template = exports["default"] = /*#__PURE__*/function () {
  function Template() {
    _classCallCheck(this, Template);
    _defineProperty(this, "astIdentifierPrefix", "__t_" + (0, _randomUtils.getRandomString)(6));
    _defineProperty(this, "symbols", new Set());
    for (var _len = arguments.length, templates = new Array(_len), _key = 0; _key < _len; _key++) {
      templates[_key] = arguments[_key];
    }
    this.templates = templates;
    this.defaultVariables = Object.create(null);
    this.requiredVariables = new Set();
    this.findRequiredVariables();
  }
  return _createClass(Template, [{
    key: "addSymbols",
    value: function addSymbols() {
      var _this = this;
      for (var _len2 = arguments.length, symbols = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
        symbols[_key2] = arguments[_key2];
      }
      symbols.forEach(function (symbol) {
        _this.symbols.add(symbol);
      });
      return this;
    }
  }, {
    key: "setDefaultVariables",
    value: function setDefaultVariables(defaultVariables) {
      this.defaultVariables = defaultVariables;
      return this;
    }
  }, {
    key: "findRequiredVariables",
    value: function findRequiredVariables() {
      var _this2 = this;
      var matches = this.templates[0].match(/{[$A-Za-z0-9_]+}/g);
      if (matches !== null) {
        matches.forEach(function (variable) {
          var name = variable.slice(1, -1);
          _this2.requiredVariables.add(name);
        });
      }
    }
  }, {
    key: "interpolateTemplate",
    value: function interpolateTemplate(variables) {
      var _this3 = this;
      var allVariables = _objectSpread(_objectSpread({}, this.defaultVariables), variables);
      var _iterator = _createForOfIteratorHelper(this.requiredVariables),
        _step;
      try {
        for (_iterator.s(); !(_step = _iterator.n()).done;) {
          var requiredVariable = _step.value;
          if (typeof allVariables[requiredVariable] === "undefined") {
            throw new Error("".concat(this.templates[0], " missing variable: ").concat(requiredVariable, " from ").concat(JSON.stringify(allVariables)));
          }
        }
      } catch (err) {
        _iterator.e(err);
      } finally {
        _iterator.f();
      }
      var template = this.templates[Math.floor(Math.random() * this.templates.length)];
      var output = template;
      this.astVariableMappings = new Map();
      Object.keys(allVariables).forEach(function (name) {
        var bracketName = "{".concat(name.replace("$", "\\$"), "}");
        var value = allVariables[name];
        if (_this3.isASTVariable(value)) {
          var astIdentifierName = _this3.astIdentifierPrefix + name;
          _this3.astVariableMappings.set(name, astIdentifierName);
          value = astIdentifierName;
        }
        var reg = new RegExp(bracketName, "g");
        output = output.replace(reg, value);
      });
      return {
        output: output,
        template: template
      };
    }
  }, {
    key: "isASTVariable",
    value: function isASTVariable(variable) {
      return typeof variable !== "string" && typeof variable !== "number";
    }
  }, {
    key: "interpolateAST",
    value: function interpolateAST(ast, variables) {
      if (this.astVariableMappings.size === 0) return;
      var allVariables = _objectSpread(_objectSpread({}, this.defaultVariables), variables);
      var template = this;

      // Reverse the lookup map
      // Before {name -> __t_m4H6nk_name}
      // After {__t_m4H6nk_name -> name}
      var reverseMappings = new Map();
      this.astVariableMappings.forEach(function (value, key) {
        reverseMappings.set(value, key);
      });
      var insertedVariables = new Set();
      (0, _traverse["default"])(ast, {
        Identifier: function Identifier(path) {
          var idName = path.node.name;
          if (!idName.startsWith(template.astIdentifierPrefix)) return;
          var variableName = reverseMappings.get(idName);
          (0, _assert.ok)(variableName, "Variable ".concat(idName, " not found in mappings"));
          var value = allVariables[variableName];
          var isSingleUse = true; // Hard-coded nodes are deemed 'single use'
          if (typeof value === "function") {
            value = value();
            isSingleUse = false;
          }
          if (value instanceof Template) {
            value = value.compile(allVariables);
            isSingleUse = false;
          }

          // Duplicate node check
          if (isSingleUse) {
            if (insertedVariables.has(variableName)) {
              (0, _assert.ok)(false, "Duplicate node inserted for variable: " + variableName);
            }
            insertedVariables.add(variableName);
          }

          // Insert new nodes
          if (!Array.isArray(value)) {
            path.replaceWith(value);
          } else {
            path.replaceWithMultiple(value);
          }
          path.skip();
        }
      });
    }
  }, {
    key: "file",
    value: function file() {
      var _this4 = this;
      var variables = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      var _this$interpolateTemp = this.interpolateTemplate(variables),
        output = _this$interpolateTemp.output;
      var file;
      try {
        file = (0, _parser.parse)(output, {
          sourceType: "module",
          allowReturnOutsideFunction: true
        });
      } catch (e) {
        throw new Error(output + "\n" + "Template failed to parse: " + e.message);
      }
      this.interpolateAST(file, variables);
      if (this.symbols.size > 0) {
        file.program.body.forEach(function (node) {
          var _iterator2 = _createForOfIteratorHelper(_this4.symbols),
            _step2;
          try {
            for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
              var symbol = _step2.value;
              node[symbol] = true;
            }
          } catch (err) {
            _iterator2.e(err);
          } finally {
            _iterator2.f();
          }
        });
      }
      return file;
    }
  }, {
    key: "compile",
    value: function compile() {
      var variables = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      var file = this.file(variables);
      return file.program.body;
    }
  }, {
    key: "single",
    value: function single() {
      var variables = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      var nodes = this.compile(variables);
      if (nodes.length !== 1) {
        var filteredNodes = nodes.filter(function (node) {
          return node.type !== "EmptyStatement";
        });
        (0, _assert.ok)(filteredNodes.length === 1, "Expected single node, got ".concat(filteredNodes.map(function (node) {
          return node.type;
        }).join(", ")));
        return filteredNodes[0];
      }
      return nodes[0];
    }
  }, {
    key: "expression",
    value: function expression() {
      var variables = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      var statement = this.single(variables);
      babelTypes.assertExpressionStatement(statement);
      return statement.expression;
    }
  }]);
}();