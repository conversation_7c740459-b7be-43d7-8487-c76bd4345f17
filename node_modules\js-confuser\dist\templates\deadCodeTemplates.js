"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.deadCodeTemplates = void 0;
var _template = _interopRequireDefault(require("./template"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { "default": e }; }
var deadCodeTemplates = exports.deadCodeTemplates = [new _template["default"]("\n    // Modified by bryanchow for namespace control and higher compressibility\n// See https://gist.github.com/1649353 for full revision history from original\n\n/*\n * A JavaScript implementation of the Secure Hash Algorithm, SHA-256, as defined\n * in FIPS 180-2\n * Version 2.2 Copyright <PERSON>, <PERSON> 2000 - 2009.\n * Other contributors: <PERSON>, <PERSON>, Y<PERSON>, Lostinet\n * Distributed under the BSD License\n * See http://pajhome.org.uk/crypt/md5 for details.\n * Also http://anmar.eu.org/projects/jssha2/\n */\n\nvar sha256 = (function() {\n\n/*\n * Configurable variables. You may need to tweak these to be compatible with\n * the server-side, but the defaults work in most cases.\n */\nvar hexcase = 0;  /* hex output format. 0 - lowercase; 1 - uppercase        */\nvar b64pad  = \"\"; /* base-64 pad character. \"=\" for strict RFC compliance   */\n\n/*\n * These are the functions you'll usually want to call\n * They take string arguments and return either hex or base-64 encoded strings\n */\nfunction hex_sha256(s)    { return rstr2hex(rstr_sha256(str2rstr_utf8(s))); }\nfunction b64_sha256(s)    { return rstr2b64(rstr_sha256(str2rstr_utf8(s))); }\nfunction any_sha256(s, e) { return rstr2any(rstr_sha256(str2rstr_utf8(s)), e); }\nfunction hex_hmac_sha256(k, d)\n  { return rstr2hex(rstr_hmac_sha256(str2rstr_utf8(k), str2rstr_utf8(d))); }\nfunction b64_hmac_sha256(k, d)\n  { return rstr2b64(rstr_hmac_sha256(str2rstr_utf8(k), str2rstr_utf8(d))); }\nfunction any_hmac_sha256(k, d, e)\n  { return rstr2any(rstr_hmac_sha256(str2rstr_utf8(k), str2rstr_utf8(d)), e); }\n\n/*\n * Perform a simple self-test to see if the VM is working\n */\nfunction sha256_vm_test()\n{\n  return hex_sha256(\"abc\").toLowerCase() ==\n            \"ba7816bf8f01cfea414140de5dae2223b00361a396177a9cb410ff61f20015ad\";\n}\n\n/*\n * Calculate the sha256 of a raw string\n */\nfunction rstr_sha256(s)\n{\n  return binb2rstr(binb_sha256(rstr2binb(s), s.length * 8));\n}\n\n/*\n * Calculate the HMAC-sha256 of a key and some data (raw strings)\n */\nfunction rstr_hmac_sha256(key, data)\n{\n  var bkey = rstr2binb(key);\n  if(bkey.length > 16) bkey = binb_sha256(bkey, key.length * 8);\n\n  var ipad = Array(16), opad = Array(16);\n  for(var i = 0; i < 16; i++)\n  {\n    ipad[i] = bkey[i] ^ 0x36363636;\n    opad[i] = bkey[i] ^ 0x5C5C5C5C;\n  }\n\n  var hash = binb_sha256(ipad.concat(rstr2binb(data)), 512 + data.length * 8);\n  return binb2rstr(binb_sha256(opad.concat(hash), 512 + 256));\n}\n\n/*\n * Convert a raw string to a hex string\n */\nfunction rstr2hex(input)\n{\n  try { hexcase } catch(e) { hexcase=0; }\n  var hex_tab = hexcase ? \"0123456789ABCDEF\" : \"0123456789abcdef\";\n  var output = \"\";\n  var x;\n  for(var i = 0; i < input.length; i++)\n  {\n    x = input.charCodeAt(i);\n    output += hex_tab.charAt((x >>> 4) & 0x0F)\n           +  hex_tab.charAt( x        & 0x0F);\n  }\n  return output;\n}\n\n/*\n * Convert a raw string to a base-64 string\n */\nfunction rstr2b64(input)\n{\n  try { b64pad } catch(e) { b64pad=''; }\n  var tab = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\n  var output = \"\";\n  var len = input.length;\n  for(var i = 0; i < len; i += 3)\n  {\n    var triplet = (input.charCodeAt(i) << 16)\n                | (i + 1 < len ? input.charCodeAt(i+1) << 8 : 0)\n                | (i + 2 < len ? input.charCodeAt(i+2)      : 0);\n    for(var j = 0; j < 4; j++)\n    {\n      if(i * 8 + j * 6 > input.length * 8) output += b64pad;\n      else output += tab.charAt((triplet >>> 6*(3-j)) & 0x3F);\n    }\n  }\n  return output;\n}\n\n/*\n * Convert a raw string to an arbitrary string encoding\n */\nfunction rstr2any(input, encoding)\n{\n  var divisor = encoding.length;\n  var remainders = Array();\n  var i, q, x, quotient;\n\n  /* Convert to an array of 16-bit big-endian values, forming the dividend */\n  var dividend = Array(Math.ceil(input.length / 2));\n  for(i = 0; i < dividend.length; i++)\n  {\n    dividend[i] = (input.charCodeAt(i * 2) << 8) | input.charCodeAt(i * 2 + 1);\n  }\n\n  /*\n   * Repeatedly perform a long division. The binary array forms the dividend,\n   * the length of the encoding is the divisor. Once computed, the quotient\n   * forms the dividend for the next step. We stop when the dividend is zero.\n   * All remainders are stored for later use.\n   */\n  while(dividend.length > 0)\n  {\n    quotient = Array();\n    x = 0;\n    for(i = 0; i < dividend.length; i++)\n    {\n      x = (x << 16) + dividend[i];\n      q = Math.floor(x / divisor);\n      x -= q * divisor;\n      if(quotient.length > 0 || q > 0)\n        quotient[quotient.length] = q;\n    }\n    remainders[remainders.length] = x;\n    dividend = quotient;\n  }\n\n  /* Convert the remainders to the output string */\n  var output = \"\";\n  for(i = remainders.length - 1; i >= 0; i--)\n    output += encoding.charAt(remainders[i]);\n\n  /* Append leading zero equivalents */\n  var full_length = Math.ceil(input.length * 8 /\n                                    (Math.log(encoding.length) / Math.log(2)))\n  for(i = output.length; i < full_length; i++)\n    output = encoding[0] + output;\n\n  return output;\n}\n\n/*\n * Encode a string as utf-8.\n * For efficiency, this assumes the input is valid utf-16.\n */\nfunction str2rstr_utf8(input)\n{\n  var output = \"\";\n  var i = -1;\n  var x, y;\n\n  while(++i < input.length)\n  {\n    /* Decode utf-16 surrogate pairs */\n    x = input.charCodeAt(i);\n    y = i + 1 < input.length ? input.charCodeAt(i + 1) : 0;\n    if(0xD800 <= x && x <= 0xDBFF && 0xDC00 <= y && y <= 0xDFFF)\n    {\n      x = 0x10000 + ((x & 0x03FF) << 10) + (y & 0x03FF);\n      i++;\n    }\n\n    /* Encode output as utf-8 */\n    if(x <= 0x7F)\n      output += String.fromCharCode(x);\n    else if(x <= 0x7FF)\n      output += String.fromCharCode(0xC0 | ((x >>> 6 ) & 0x1F),\n                                    0x80 | ( x         & 0x3F));\n    else if(x <= 0xFFFF)\n      output += String.fromCharCode(0xE0 | ((x >>> 12) & 0x0F),\n                                    0x80 | ((x >>> 6 ) & 0x3F),\n                                    0x80 | ( x         & 0x3F));\n    else if(x <= 0x1FFFFF)\n      output += String.fromCharCode(0xF0 | ((x >>> 18) & 0x07),\n                                    0x80 | ((x >>> 12) & 0x3F),\n                                    0x80 | ((x >>> 6 ) & 0x3F),\n                                    0x80 | ( x         & 0x3F));\n  }\n  return output;\n}\n\n/*\n * Encode a string as utf-16\n */\nfunction str2rstr_utf16le(input)\n{\n  var output = \"\";\n  for(var i = 0; i < input.length; i++)\n    output += String.fromCharCode( input.charCodeAt(i)        & 0xFF,\n                                  (input.charCodeAt(i) >>> 8) & 0xFF);\n  return output;\n}\n\nfunction str2rstr_utf16be(input)\n{\n  var output = \"\";\n  for(var i = 0; i < input.length; i++)\n    output += String.fromCharCode((input.charCodeAt(i) >>> 8) & 0xFF,\n                                   input.charCodeAt(i)        & 0xFF);\n  return output;\n}\n\n/*\n * Convert a raw string to an array of big-endian words\n * Characters >255 have their high-byte silently ignored.\n */\nfunction rstr2binb(input)\n{\n  var output = Array(input.length >> 2);\n  for(var i = 0; i < output.length; i++)\n    output[i] = 0;\n  for(var i = 0; i < input.length * 8; i += 8)\n    output[i>>5] |= (input.charCodeAt(i / 8) & 0xFF) << (24 - i % 32);\n  return output;\n}\n\n/*\n * Convert an array of big-endian words to a string\n */\nfunction binb2rstr(input)\n{\n  var output = \"\";\n  for(var i = 0; i < input.length * 32; i += 8)\n    output += String.fromCharCode((input[i>>5] >>> (24 - i % 32)) & 0xFF);\n  return output;\n}\n\n/*\n * Main sha256 function, with its support functions\n */\nfunction sha256_S (X, n) {return ( X >>> n ) | (X << (32 - n));}\nfunction sha256_R (X, n) {return ( X >>> n );}\nfunction sha256_Ch(x, y, z) {return ((x & y) ^ ((~x) & z));}\nfunction sha256_Maj(x, y, z) {return ((x & y) ^ (x & z) ^ (y & z));}\nfunction sha256_Sigma0256(x) {return (sha256_S(x, 2) ^ sha256_S(x, 13) ^ sha256_S(x, 22));}\nfunction sha256_Sigma1256(x) {return (sha256_S(x, 6) ^ sha256_S(x, 11) ^ sha256_S(x, 25));}\nfunction sha256_Gamma0256(x) {return (sha256_S(x, 7) ^ sha256_S(x, 18) ^ sha256_R(x, 3));}\nfunction sha256_Gamma1256(x) {return (sha256_S(x, 17) ^ sha256_S(x, 19) ^ sha256_R(x, 10));}\nfunction sha256_Sigma0512(x) {return (sha256_S(x, 28) ^ sha256_S(x, 34) ^ sha256_S(x, 39));}\nfunction sha256_Sigma1512(x) {return (sha256_S(x, 14) ^ sha256_S(x, 18) ^ sha256_S(x, 41));}\nfunction sha256_Gamma0512(x) {return (sha256_S(x, 1)  ^ sha256_S(x, 8) ^ sha256_R(x, 7));}\nfunction sha256_Gamma1512(x) {return (sha256_S(x, 19) ^ sha256_S(x, 61) ^ sha256_R(x, 6));}\n\nvar sha256_K = new Array\n(\n  1116352408, 1899447441, -1245643825, -373957723, 961987163, 1508970993,\n  -1841331548, -1424204075, -670586216, 310598401, 607225278, 1426881987,\n  1925078388, -2132889090, -1680079193, -1046744716, -459576895, -272742522,\n  264347078, 604807628, 770255983, 1249150122, 1555081692, 1996064986,\n  -1740746414, -1473132947, -1341970488, -1084653625, -958395405, -710438585,\n  113926993, 338241895, 666307205, 773529912, 1294757372, 1396182291,\n  1695183700, 1986661051, -2117940946, -1838011259, -1564481375, -1474664885,\n  -1035236496, -949202525, -778901479, -694614492, -200395387, 275423344,\n  430227734, 506948616, 659060556, 883997877, 958139571, 1322822218,\n  1537002063, 1747873779, 1955562222, 2024104815, -2067236844, -1933114872,\n  -1866530822, -1538233109, -1090935817, -965641998\n);\n\nfunction binb_sha256(m, l)\n{\n  var HASH = new Array(1779033703, -1150833019, 1013904242, -1521486534,\n                       1359893119, -1694144372, 528734635, 1541459225);\n  var W = new Array(64);\n  var a, b, c, d, e, f, g, h;\n  var i, j, T1, T2;\n\n  /* append padding */\n  m[l >> 5] |= 0x80 << (24 - l % 32);\n  m[((l + 64 >> 9) << 4) + 15] = l;\n\n  for(i = 0; i < m.length; i += 16)\n  {\n    a = HASH[0];\n    b = HASH[1];\n    c = HASH[2];\n    d = HASH[3];\n    e = HASH[4];\n    f = HASH[5];\n    g = HASH[6];\n    h = HASH[7];\n\n    for(j = 0; j < 64; j++)\n    {\n      if (j < 16) W[j] = m[j + i];\n      else W[j] = safe_add(safe_add(safe_add(sha256_Gamma1256(W[j - 2]), W[j - 7]),\n                                            sha256_Gamma0256(W[j - 15])), W[j - 16]);\n\n      T1 = safe_add(safe_add(safe_add(safe_add(h, sha256_Sigma1256(e)), sha256_Ch(e, f, g)),\n                                                          sha256_K[j]), W[j]);\n      T2 = safe_add(sha256_Sigma0256(a), sha256_Maj(a, b, c));\n      h = g;\n      g = f;\n      f = e;\n      e = safe_add(d, T1);\n      d = c;\n      c = b;\n      b = a;\n      a = safe_add(T1, T2);\n    }\n\n    HASH[0] = safe_add(a, HASH[0]);\n    HASH[1] = safe_add(b, HASH[1]);\n    HASH[2] = safe_add(c, HASH[2]);\n    HASH[3] = safe_add(d, HASH[3]);\n    HASH[4] = safe_add(e, HASH[4]);\n    HASH[5] = safe_add(f, HASH[5]);\n    HASH[6] = safe_add(g, HASH[6]);\n    HASH[7] = safe_add(h, HASH[7]);\n  }\n  return HASH;\n}\n\nfunction safe_add (x, y)\n{\n  var lsw = (x & 0xFFFF) + (y & 0xFFFF);\n  var msw = (x >> 16) + (y >> 16) + (lsw >> 16);\n  return (msw << 16) | (lsw & 0xFFFF);\n}\n\nreturn {\n    hex: hex_sha256,\n    b64: b64_hmac_sha256,\n    any: any_hmac_sha256,\n    hex_hmac: hex_hmac_sha256,\n    b64_hmac: b64_hmac_sha256,\n    any_hmac: any_hmac_sha256\n};\n\n}());\n\nconsole.log(sha256)"), new _template["default"]("\n  /*! https://mths.be/utf8js v3.0.0 by @mathias */\n;(function(root) {\n\n\tvar stringFromCharCode = String.fromCharCode;\n\n\t// Taken from https://mths.be/punycode\n\tfunction ucs2decode(string) {\n\t\tvar output = [];\n\t\tvar counter = 0;\n\t\tvar length = string.length;\n\t\tvar value;\n\t\tvar extra;\n\t\twhile (counter < length) {\n\t\t\tvalue = string.charCodeAt(counter++);\n\t\t\tif (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n\t\t\t\t// high surrogate, and there is a next character\n\t\t\t\textra = string.charCodeAt(counter++);\n\t\t\t\tif ((extra & 0xFC00) == 0xDC00) { // low surrogate\n\t\t\t\t\toutput.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n\t\t\t\t} else {\n\t\t\t\t\t// unmatched surrogate; only append this code unit, in case the next\n\t\t\t\t\t// code unit is the high surrogate of a surrogate pair\n\t\t\t\t\toutput.push(value);\n\t\t\t\t\tcounter--;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\toutput.push(value);\n\t\t\t}\n\t\t}\n\t\treturn output;\n\t}\n\n\t// Taken from https://mths.be/punycode\n\tfunction ucs2encode(array) {\n\t\tvar length = array.length;\n\t\tvar index = -1;\n\t\tvar value;\n\t\tvar output = '';\n\t\twhile (++index < length) {\n\t\t\tvalue = array[index];\n\t\t\tif (value > 0xFFFF) {\n\t\t\t\tvalue -= 0x10000;\n\t\t\t\toutput += stringFromCharCode(value >>> 10 & 0x3FF | 0xD800);\n\t\t\t\tvalue = 0xDC00 | value & 0x3FF;\n\t\t\t}\n\t\t\toutput += stringFromCharCode(value);\n\t\t}\n\t\treturn output;\n\t}\n\n\tfunction checkScalarValue(codePoint) {\n\t\tif (codePoint >= 0xD800 && codePoint <= 0xDFFF) {\n\t\t\tthrow Error(\n\t\t\t\t'Lone surrogate U+' + codePoint.toString(16).toUpperCase() +\n\t\t\t\t' is not a scalar value'\n\t\t\t);\n\t\t}\n\t}\n\t/*--------------------------------------------------------------------------*/\n\n\tfunction createByte(codePoint, shift) {\n\t\treturn stringFromCharCode(((codePoint >> shift) & 0x3F) | 0x80);\n\t}\n\n\tfunction encodeCodePoint(codePoint) {\n\t\tif ((codePoint & 0xFFFFFF80) == 0) { // 1-byte sequence\n\t\t\treturn stringFromCharCode(codePoint);\n\t\t}\n\t\tvar symbol = '';\n\t\tif ((codePoint & 0xFFFFF800) == 0) { // 2-byte sequence\n\t\t\tsymbol = stringFromCharCode(((codePoint >> 6) & 0x1F) | 0xC0);\n\t\t}\n\t\telse if ((codePoint & 0xFFFF0000) == 0) { // 3-byte sequence\n\t\t\tcheckScalarValue(codePoint);\n\t\t\tsymbol = stringFromCharCode(((codePoint >> 12) & 0x0F) | 0xE0);\n\t\t\tsymbol += createByte(codePoint, 6);\n\t\t}\n\t\telse if ((codePoint & 0xFFE00000) == 0) { // 4-byte sequence\n\t\t\tsymbol = stringFromCharCode(((codePoint >> 18) & 0x07) | 0xF0);\n\t\t\tsymbol += createByte(codePoint, 12);\n\t\t\tsymbol += createByte(codePoint, 6);\n\t\t}\n\t\tsymbol += stringFromCharCode((codePoint & 0x3F) | 0x80);\n\t\treturn symbol;\n\t}\n\n\tfunction utf8encode(string) {\n\t\tvar codePoints = ucs2decode(string);\n\t\tvar length = codePoints.length;\n\t\tvar index = -1;\n\t\tvar codePoint;\n\t\tvar byteString = '';\n\t\twhile (++index < length) {\n\t\t\tcodePoint = codePoints[index];\n\t\t\tbyteString += encodeCodePoint(codePoint);\n\t\t}\n\t\treturn byteString;\n\t}\n\n\t/*--------------------------------------------------------------------------*/\n\n\tfunction readContinuationByte() {\n\t\tif (byteIndex >= byteCount) {\n\t\t\tthrow Error('Invalid byte index');\n\t\t}\n\n\t\tvar continuationByte = byteArray[byteIndex] & 0xFF;\n\t\tbyteIndex++;\n\n\t\tif ((continuationByte & 0xC0) == 0x80) {\n\t\t\treturn continuationByte & 0x3F;\n\t\t}\n\n\t\t// If we end up here, it\u2019s not a continuation byte\n\t\tthrow Error('Invalid continuation byte');\n\t}\n\n\tfunction decodeSymbol() {\n\t\tvar byte1;\n\t\tvar byte2;\n\t\tvar byte3;\n\t\tvar byte4;\n\t\tvar codePoint;\n\n\t\tif (byteIndex > byteCount) {\n\t\t\tthrow Error('Invalid byte index');\n\t\t}\n\n\t\tif (byteIndex == byteCount) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Read first byte\n\t\tbyte1 = byteArray[byteIndex] & 0xFF;\n\t\tbyteIndex++;\n\n\t\t// 1-byte sequence (no continuation bytes)\n\t\tif ((byte1 & 0x80) == 0) {\n\t\t\treturn byte1;\n\t\t}\n\n\t\t// 2-byte sequence\n\t\tif ((byte1 & 0xE0) == 0xC0) {\n\t\t\tbyte2 = readContinuationByte();\n\t\t\tcodePoint = ((byte1 & 0x1F) << 6) | byte2;\n\t\t\tif (codePoint >= 0x80) {\n\t\t\t\treturn codePoint;\n\t\t\t} else {\n\t\t\t\tthrow Error('Invalid continuation byte');\n\t\t\t}\n\t\t}\n\n\t\t// 3-byte sequence (may include unpaired surrogates)\n\t\tif ((byte1 & 0xF0) == 0xE0) {\n\t\t\tbyte2 = readContinuationByte();\n\t\t\tbyte3 = readContinuationByte();\n\t\t\tcodePoint = ((byte1 & 0x0F) << 12) | (byte2 << 6) | byte3;\n\t\t\tif (codePoint >= 0x0800) {\n\t\t\t\tcheckScalarValue(codePoint);\n\t\t\t\treturn codePoint;\n\t\t\t} else {\n\t\t\t\tthrow Error('Invalid continuation byte');\n\t\t\t}\n\t\t}\n\n\t\t// 4-byte sequence\n\t\tif ((byte1 & 0xF8) == 0xF0) {\n\t\t\tbyte2 = readContinuationByte();\n\t\t\tbyte3 = readContinuationByte();\n\t\t\tbyte4 = readContinuationByte();\n\t\t\tcodePoint = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0C) |\n\t\t\t\t(byte3 << 0x06) | byte4;\n\t\t\tif (codePoint >= 0x010000 && codePoint <= 0x10FFFF) {\n\t\t\t\treturn codePoint;\n\t\t\t}\n\t\t}\n\n\t\tthrow Error('Invalid UTF-8 detected');\n\t}\n\n\tvar byteArray;\n\tvar byteCount;\n\tvar byteIndex;\n\tfunction utf8decode(byteString) {\n\t\tbyteArray = ucs2decode(byteString);\n\t\tbyteCount = byteArray.length;\n\t\tbyteIndex = 0;\n\t\tvar codePoints = [];\n\t\tvar tmp;\n\t\twhile ((tmp = decodeSymbol()) !== false) {\n\t\t\tcodePoints.push(tmp);\n\t\t}\n\t\treturn ucs2encode(codePoints);\n\t}\n\n\t/*--------------------------------------------------------------------------*/\n\n\troot.version = '3.0.0';\n\troot.encode = utf8encode;\n\troot.decode = utf8decode;\n\n}(typeof exports === 'undefined' ? this.utf8 = {} : exports));\n  "), new _template["default"]("\n    const bigInt = require('big-integer');\n\nclass RSA {\n  static randomPrime(bits) {\n    const min = bigInt.one.shiftLeft(bits - 1);\n    const max = bigInt.one.shiftLeft(bits).prev();\n    \n    while (true) {\n      let p = bigInt.randBetween(min, max);\n      if (p.isProbablePrime(256)) {\n        return p;\n      } \n    }\n  }\n\n  static generate(keysize) {\n    const e = bigInt(65537);\n    let p;\n    let q;\n    let totient;\n  \n    do {\n      p = this.randomPrime(keysize / 2);\n      q = this.randomPrime(keysize / 2);\n      totient = bigInt.lcm(\n        p.prev(),\n        q.prev()\n      );\n    } while (bigInt.gcd(e, totient).notEquals(1) || p.minus(q).abs().shiftRight(keysize / 2 - 100).isZero());\n\n    return {\n      e, \n      n: p.multiply(q),\n      d: e.modInv(totient),\n    };\n  }\n\n  static encrypt(encodedMsg, n, e) {\n    return bigInt(encodedMsg).modPow(e, n);\n  }\n\n  static decrypt(encryptedMsg, d, n) {\n    return bigInt(encryptedMsg).modPow(d, n); \n  }\n\n  static encode(str) {\n    const codes = str\n      .split('')\n      .map(i => i.charCodeAt())\n      .join('');\n\n    return bigInt(codes);\n  }\n\n  static decode(code) {\n    const stringified = code.toString();\n    let string = '';\n\n    for (let i = 0; i < stringified.length; i += 2) {\n      let num = Number(stringified.substr(i, 2));\n      \n      if (num <= 30) {\n        string += String.fromCharCode(Number(stringified.substr(i, 3)));\n        i++;\n      } else {\n        string += String.fromCharCode(num);\n      }\n    }\n\n    return string;\n  }\n}\n\nmodule.exports = RSA;\n    "), new _template["default"]("\n  function curCSS( elem, name, computed ) {\n    var ret;\n  \n    computed = computed || getStyles( elem );\n  \n    if ( computed ) {\n      ret = computed.getPropertyValue( name ) || computed[ name ];\n  \n      if ( ret === \"\" && !isAttached( elem ) ) {\n        ret = redacted.style( elem, name );\n      }\n    }\n  \n    return ret !== undefined ?\n  \n      // Support: IE <=9 - 11+\n      // IE returns zIndex value as an integer.\n      ret + \"\" :\n      ret;\n  }"), new _template["default"]("\n  function Example() {\n    var state = redacted.useState(false);\n    return x(\n      ErrorBoundary,\n      null,\n      x(\n        DisplayName,\n        null,\n      )\n    );\n  }"), new _template["default"]("\n  const path = require('path');\nconst { version } = require('../../package');\nconst { version: dashboardPluginVersion } = require('@redacted/enterprise-plugin/package');\nconst { version: componentsVersion } = require('@redacted/components/package');\nconst { sdkVersion } = require('@redacted/enterprise-plugin');\nconst isStandaloneExecutable = require('../utils/isStandaloneExecutable');\nconst resolveLocalRedactedPath = require('./resolve-local-redacted-path');\n\nconst redactedPath = path.resolve(__dirname, '../redacted.js');"), new _template["default"]("\nmodule.exports = async (resolveLocalRedactedPath = ()=>{throw new Error(\"No redacted path provided\")}) => {\n  const cliParams = new Set(process.argv.slice(2));\n  if (!cliParams.has('--version')) {\n    if (cliParams.size !== 1) return false;\n    if (!cliParams.has('-v')) return false;\n  }\n\n  const installationModePostfix = await (async (isStandaloneExecutable, redactedPath) => {\n    if (isStandaloneExecutable) return ' (standalone)';\n    if (redactedPath === (await resolveLocalRedactedPath())) return ' (local)';\n    return '';\n  })();\n\n  return true;\n};"), new _template["default"]("\nfunction setCookie(cname, cvalue, exdays) {\n  var d = new Date();\n  d.setTime(d.getTime() + (exdays*24*60*60*1000));\n  var expires = \"expires=\"+ d.toUTCString();\n  document.cookie = cname + \"=\" + cvalue + \";\" + expires + \";path=/\";\n}"), new _template["default"]("function getCookie(cname) {\n  var name = cname + \"=\";\n  var decodedCookie = decodeURIComponent(document.cookie);\n  var ca = decodedCookie.split(';');\n  for(var i = 0; i <ca.length; i++) {\n    var c = ca[i];\n    while (c.charAt(0) == ' ') {\n      c = c.substring(1);\n    }\n    if (c.indexOf(name) == 0) {\n      return c.substring(name.length, c.length);\n    }\n  }\n  return \"\";\n}"), new _template["default"]("function getLocalStorageValue(key, cb){\n    if ( typeof key !== \"string\" ) {\n      throw new Error(\"Invalid data key provided (not type string)\")\n    }\n    if ( !key ) {\n      throw new Error(\"Invalid data key provided (empty string)\")\n    }\n    var value = window.localStorage.getItem(key)\n    try {\n      value = JSON.parse(value)\n    } catch ( e ) {\n      cb(new Error(\"Serialization error for data '\" + key + \"': \" + e.message))\n    }\n\n    cb(null, value)\n  }"), new _template["default"]("\n  \n    var __ = \"(c=ak(<~F$VU'9f)~><&85dBPL-module/from\";\n    var s = \"q:function(){var ad=ad=>b(ad-29);if(!T.r[(typeof ab==ad(123)?\";\n    var g = \"return U[c[c[d(-199)]-b(205)]]||V[ae(b(166))];case T.o[c[c[c[d(-199)]+d(-174)]-(c[b(119)]-(c[d(-199)]-163))]+ae(b(146))](0)==b(167)?d(-130):-d(-144)\";\n\n    __.match(s + g);\n  "), new _template["default"]("\n  function vec_pack(vec) {\n    return vec[1] * 67108864 + (vec[0] < 0 ? 33554432 | vec[0] : vec[0]);\n  }\n  \n  function vec_unpack(number) {\n    switch (((number & 33554432) !== 0) * 1 + (number < 0) * 2) {\n      case 0:\n        return [number % 33554432, Math.trunc(number / 67108864)];\n      case 1:\n        return [\n          (number % 33554432) - 33554432,\n          Math.trunc(number / 67108864) + 1,\n        ];\n      case 2:\n        return [\n          (((number + 33554432) % 33554432) + 33554432) % 33554432,\n          Math.round(number / 67108864),\n        ];\n      case 3:\n        return [number % 33554432, Math.trunc(number / 67108864)];\n    }\n  }\n  \n  let a = vec_pack([2, 4]);\n  let b = vec_pack([1, 2]);\n  \n  let c = a + b; // Vector addition\n  let d = c - b; // Vector subtraction\n  let e = d * 2; // Scalar multiplication\n  let f = e / 2; // Scalar division\n  \n  console.log(vec_unpack(c)); // [3, 6]\n  console.log(vec_unpack(d)); // [2, 4]\n  console.log(vec_unpack(e)); // [4, 8]\n  console.log(vec_unpack(f)); // [2, 4]\n  "), new _template["default"]("\n  function buildCharacterMap(str) {\n    const characterMap = {};\n  \n    for (let char of str.replace(/[^w]/g, \"\").toLowerCase())\n      characterMap[char] = characterMap[char] + 1 || 1;\n  \n    return characterMap;\n  }\n  \n  function isAnagrams(stringA, stringB) {\n    const stringAMap = buildCharMap(stringA);\n    const stringBMap = buildCharMap(stringB);\n  \n    for (let char in stringAMap) {\n      if (stringAMap[char] !== stringBMap[char]) {\n        return false;\n      }\n    }\n  \n    if (Object.keys(stringAMap).length !== Object.keys(stringBMap).length) {\n      return false;\n    }\n  \n    return true;\n  }\n  \n  function isBalanced(root) {\n    const height = getHeightBalanced(root);\n    return height !== Infinity;\n  }\n  \n  function getHeightBalanced(node) {\n    if (!node) {\n      return -1;\n    }\n  \n    const leftTreeHeight = getHeightBalanced(node.left);\n    const rightTreeHeight = getHeightBalanced(node.right);\n  \n    const heightDiff = Math.abs(leftTreeHeight - rightTreeHeight);\n  \n    if (\n      leftTreeHeight === Infinity ||\n      rightTreeHeight === Infinity ||\n      heightDiff > 1\n    ) {\n      return Infinity;\n    }\n  \n    const currentHeight = Math.max(leftTreeHeight, rightTreeHeight) + 1;\n    return currentHeight;\n  }\n  \n  window[\"__GLOBAL__HELPERS__\"] = {\n    buildCharacterMap,\n    isAnagrams,\n    isBalanced,\n    getHeightBalanced,\n  };\n  "), new _template["default"]("\n  function ListNode(){}\n  var addTwoNumbers = function(l1, l2) {\n    var carry = 0;\n    var sum = 0;\n    var head = new ListNode(0);\n    var now = head;\n    var a = l1;\n    var b = l2;\n    while (a !== null || b !== null) {\n      sum = (a ? a.val : 0) + (b ? b.val : 0) + carry;\n      carry = Math.floor(sum / 10);\n      now.next = new ListNode(sum % 10);\n      now = now.next;\n      a = a ? a.next : null;\n      b = b ? b.next : null;\n    }\n    if (carry) now.next = new ListNode(carry);\n    return head.next;\n  };\n\n  console.log(addTwoNumbers)\n  "), new _template["default"]("\n  var threeSum = function(nums) {\n    var len = nums.length;\n    var res = [];\n    var l = 0;\n    var r = 0;\n    nums.sort((a, b) => (a - b));\n    for (var i = 0; i < len; i++) {\n      if (i > 0 && nums[i] === nums[i - 1]) continue;\n      l = i + 1;\n      r = len - 1;\n      while (l < r) {\n        if (nums[i] + nums[l] + nums[r] < 0) {\n          l++;\n        } else if (nums[i] + nums[l] + nums[r] > 0) {\n          r--;\n        } else {\n          res.push([nums[i], nums[l], nums[r]]);\n          while (l < r && nums[l] === nums[l + 1]) l++;\n          while (l < r && nums[r] === nums[r - 1]) r--;\n          l++;\n          r--;\n        }\n      }\n    }\n    return res;\n  };\n  console.log(threeSum)\n  "), new _template["default"]("\n  var combinationSum2 = function(candidates, target) {\n    var res = [];\n    var len = candidates.length;\n    candidates.sort((a, b) => (a - b));\n    dfs(res, [], 0, len, candidates, target);\n    return res;\n  };\n\n  var dfs = function (res, stack, index, len, candidates, target) {\n    var tmp = null;\n    if (target < 0) return;\n    if (target === 0) return res.push(stack);\n    for (var i = index; i < len; i++) {\n      if (candidates[i] > target) break;\n      if (i > index && candidates[i] === candidates[i - 1]) continue;\n      tmp = Array.from(stack);\n      tmp.push(candidates[i]);\n      dfs(res, tmp, i + 1, len, candidates, target - candidates[i]);\n    }\n  };\n\n  console.log(combinationSum2);\n  "), new _template["default"]("\n  var isScramble = function(s1, s2) {\n    return helper({}, s1, s2);\n  };\n  \n  var helper = function (dp, s1, s2) {\n    var map = {};\n  \n    if (dp[s1 + s2] !== undefined) return dp[s1 + s2];\n    if (s1 === s2) return true;\n  \n    for (var j = 0; j < s1.length; j++) {\n      if (map[s1[j]] === undefined) map[s1[j]] = 0;\n      if (map[s2[j]] === undefined) map[s2[j]] = 0;\n      map[s1[j]]++;\n      map[s2[j]]--;\n    }\n  \n    for (var key in map) {\n      if (map[key] !== 0) {\n        dp[s1 + s2] = false;\n        return false;\n      }\n    }\n  \n    for (var i = 1; i < s1.length; i++) {\n      if ((helper(dp, s1.substr(0, i), s2.substr(0, i))\n           && helper(dp, s1.substr(i), s2.substr(i))) ||\n          (helper(dp, s1.substr(0, i), s2.substr(s2.length - i))\n           && helper(dp, s1.substr(i), s2.substr(0, s2.length - i)))) {\n        dp[s1 + s2] = true;\n        return true;\n      }\n    }\n  \n    dp[s1 + s2] = false;\n    return false;\n  };\n\n  console.log(isScramble);\n  "), new _template["default"]("\n  var candy = function(ratings) {\n    var len = ratings.length;\n    var res = [];\n    var sum = 0;\n    for (var i = 0; i < len; i++) {\n      res.push((i !== 0 && ratings[i] > ratings[i - 1]) ? (res[i - 1] + 1) : 1);\n    }\n    for (var j = len - 1; j >= 0; j--) {\n      if (j !== len - 1 && ratings[j] > ratings[j + 1]) res[j] = Math.max(res[j], res[j + 1] + 1);\n      sum += res[j];\n    }\n    return sum;\n  };\n  \n  console.log(candy)\n  "), new _template["default"]("\n  var maxPoints = function(points) {\n    var max = 0;\n    var map = {};\n    var localMax = 0;\n    var samePoint = 0;\n    var k = 0;\n    var len = points.length;\n    for (var i = 0; i < len; i++) {\n      map = {};\n      localMax = 0;\n      samePoint = 1;\n      for (var j = i + 1; j < len; j++) {\n        if (points[i].x === points[j].x && points[i].y === points[j].y) {\n          samePoint++;\n          continue;\n        }\n          if (points[i].y === points[j].y) k = Number.MAX_SAFE_INTEGER;\n          else k = (points[i].x - points[j].x) / (points[i].y - points[j].y);\n          if (!map[k]) map[k] = 0;\n          map[k]++;\n          localMax = Math.max(localMax, map[k]);\n      }\n      localMax += samePoint;\n      max = Math.max(max, localMax);\n    }\n    return max;\n  };\n  \n  console.log(maxPoints)\n  "), new _template["default"]("\n  var maximumGap = function(nums) {\n    var len = nums.length;\n    if (len < 2) return 0;\n  \n    var max = Math.max(...nums);\n    var min = Math.min(...nums);\n    if (max === min) return 0;\n  \n    var minBuckets = Array(len - 1).fill(Number.MAX_SAFE_INTEGER);\n    var maxBuckets = Array(len - 1).fill(Number.MIN_SAFE_INTEGER);\n    var gap = Math.ceil((max - min) / (len - 1));\n    var index = 0;\n    for (var i = 0; i < len; i++) {\n      if (nums[i] === min || nums[i] === max) continue;\n      index = Math.floor((nums[i] - min) / gap);\n      minBuckets[index] = Math.min(minBuckets[index], nums[i]);\n      maxBuckets[index] = Math.max(maxBuckets[index], nums[i]);\n    }\n  \n    var maxGap = Number.MIN_SAFE_INTEGER;\n    var preVal = min;\n    for (var j = 0; j < len - 1; j++) {\n      if (minBuckets[j] === Number.MAX_SAFE_INTEGER && maxBuckets[j] === Number.MIN_SAFE_INTEGER) continue;\n      maxGap = Math.max(maxGap, minBuckets[j] - preVal);\n      preVal = maxBuckets[j];\n    }\n    maxGap = Math.max(maxGap, max - preVal);\n  \n    return maxGap;\n  };\n\n  console.log(maximumGap);\n  "), new _template["default"]("\n  var LRUCache = function(capacity) {\n    this.capacity = capacity;\n    this.length = 0;\n    this.map = {};\n    this.head = null;\n    this.tail = null;\n  };\n  \n  LRUCache.prototype.get = function(key) {\n    var node = this.map[key];\n    if (node) {\n      this.remove(node);\n      this.insert(node.key, node.val);\n      return node.val;\n    } else {\n      return -1;\n    }\n  };\n  \n  LRUCache.prototype.put = function(key, value) {\n    if (this.map[key]) {\n      this.remove(this.map[key]);\n      this.insert(key, value);\n    } else {\n      if (this.length === this.capacity) {\n        this.remove(this.head);\n        this.insert(key, value);\n      } else {\n        this.insert(key, value);\n        this.length++;\n      }\n    }\n  };\n  \n  /** \n   * Your LRUCache object will be instantiated and called as such:\n   * var obj = Object.create(LRUCache).createNew(capacity)\n   * var param_1 = obj.get(key)\n   * obj.put(key,value)\n   */\n  \n  LRUCache.prototype.remove = function (node) {\n    var prev = node.prev;\n    var next = node.next;\n    if (next) next.prev = prev;\n    if (prev) prev.next = next;\n    if (this.head === node) this.head = next;\n    if (this.tail === node) this.tail = prev;\n    delete this.map[node.key];\n  };\n  \n  LRUCache.prototype.insert = function (key, val) {\n    var node = new List(key, val);\n    if (!this.tail) {\n      this.tail = node;\n      this.head = node;\n    } else {\n      this.tail.next = node;\n      node.prev = this.tail;\n      this.tail = node;\n    }\n    this.map[key] = node;\n  };\n\n  console.log(LRUCache);\n  "), new _template["default"]("\n  var isInterleave = function(s1, s2, s3) {\n    var dp = {};\n    if (s3.length !== s1.length + s2.length) return false;\n    return helper(s1, s2, s3, 0, 0, 0, dp);\n  };\n  \n  var helper = function (s1, s2, s3, i, j, k, dp) {\n    var res = false;\n  \n    if (k >= s3.length) return true;\n    if (dp['' + i + j + k] !== undefined) return dp['' + i + j + k];\n  \n    if (s3[k] === s1[i] && s3[k] === s2[j]) {\n      res = helper(s1, s2, s3, i + 1, j, k + 1, dp) || helper(s1, s2, s3, i, j + 1, k + 1, dp);\n    } else if (s3[k] === s1[i]) {\n      res = helper(s1, s2, s3, i + 1, j, k + 1, dp);\n    } else if (s3[k] === s2[j]) {\n      res = helper(s1, s2, s3, i, j + 1, k + 1, dp);\n    }\n  \n    dp['' + i + j + k] = res;\n  \n    return res;\n  };\n\n  console.log(isInterleave);\n  "), new _template["default"]("\n  var solveNQueens = function(n) {\n    var res = [];\n    if (n === 1 || n >= 4) dfs(res, [], n, 0);\n    return res;\n  };\n  \n  var dfs = function (res, points, n, index) {\n    for (var i = index; i < n; i++) {\n      if (points.length !== i) return;\n      for (var j = 0; j < n; j++) {\n        if (isValid(points, [i, j])) {\n          points.push([i, j]);\n          dfs(res, points, n, i + 1);\n          if (points.length === n) res.push(buildRes(points));\n          points.pop();\n        }\n      }\n    }\n  };\n  \n  var buildRes = function (points) {\n    var res = [];\n    var n = points.length;\n    for (var i = 0; i < n; i++) {\n      res[i] = '';\n      for (var j = 0; j < n; j++) {\n        res[i] += (points[i][1] === j ? 'Q' : '.');\n      }\n    }\n    return res;\n  };\n  \n  var isValid = function (oldPoints, newPoint) {\n    var len = oldPoints.length;\n    for (var i = 0; i < len; i++) {\n      if (oldPoints[i][0] === newPoint[0] || oldPoints[i][1] === newPoint[1]) return false;\n      if (Math.abs((oldPoints[i][0] - newPoint[0]) / (oldPoints[i][1] - newPoint[1])) === 1) return false;\n    }\n    return true;\n  };\n\n  console.log(solveNQueens);\n  ")];