"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = pack;
var t = _interopRequireWildcard(require("@babel/types"));
var _obfuscator = _interopRequireDefault(require("../obfuscator"));
var _template = _interopRequireDefault(require("../templates/template"));
var _astUtils = require("../utils/ast-utils");
var _constants = require("../constants");
var _order = require("../order");
function _interopRequireDefault(e) { return e && e.__esModule ? e : { "default": e }; }
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { "default": e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n["default"] = e, t && t.set(e, n), n; }
function _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t["return"] && (u = t["return"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }
function _arrayWithHoles(r) { if (Array.isArray(r)) return r; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function pack(_ref) {
  var _me$obfuscator$parent;
  var Plugin = _ref.Plugin;
  var me = Plugin(_order.Order.Pack, {
    changeData: {
      globals: 0
    }
  });

  // RGF functions will re-use parent Pack Interface
  var packInterface = (_me$obfuscator$parent = me.obfuscator.parentObfuscator) === null || _me$obfuscator$parent === void 0 ? void 0 : _me$obfuscator$parent.packInterface;

  // Create new Pack Interface (root)
  if (!packInterface) {
    packInterface = {
      objectName: me.obfuscator.nameGen.generate(),
      mappings: new Map(),
      setterPropsNeeded: new Set(),
      typeofMappings: new Map()
    };
    me.obfuscator.packInterface = packInterface;
  }
  var _packInterface = packInterface,
    _objectName = _packInterface.objectName,
    mappings = _packInterface.mappings,
    setterPropsNeeded = _packInterface.setterPropsNeeded,
    typeofMappings = _packInterface.typeofMappings;
  var prependNodes = [];
  return {
    // Transform identifiers, preserve import statements
    visitor: {
      ImportDeclaration: function ImportDeclaration(path) {
        prependNodes.push(path.node);
        path.remove();

        // Ensure bindings are removed -> variable becomes a global -> added to mappings object
        path.scope.crawl();
      },
      // TODO: Add support for export statements
      "ExportNamedDeclaration|ExportDefaultDeclaration|ExportAllDeclaration": function ExportNamedDeclarationExportDefaultDeclarationExportAllDeclaration(path) {
        me.error("Export statements are not supported in packed code.");
      },
      Program: function Program(path) {
        path.scope.crawl();
      },
      Identifier: {
        exit: function exit(path) {
          if (!(0, _astUtils.isVariableIdentifier)(path)) return;
          if ((0, _astUtils.isDefiningIdentifier)(path)) return;
          if (path.node[_constants.GEN_NODE]) return;
          if (path.node[_constants.WITH_STATEMENT]) return;
          var identifierName = path.node.name;
          if (_constants.reservedIdentifiers.has(identifierName)) return;
          if (me.options.target === "node" && _constants.reservedNodeModuleIdentifiers.has(identifierName)) {
            // Allow module.exports and require
          } else {
            if (me.options.globalVariables.has(identifierName)) return;
          }
          if (identifierName === _constants.variableFunctionName) return;
          if (identifierName === _objectName) return;
          if (!path.scope.hasGlobal(identifierName)) return;
          if (path.scope.hasBinding(identifierName)) return;

          // Check user's custom implementation
          if (!me.computeProbabilityMap(me.options.pack, identifierName)) return;
          if (path.key === "argument" && path.parentPath.isUnaryExpression({
            operator: "typeof"
          })) {
            var unaryExpression = path.parentPath;
            var _propertyName = typeofMappings.get(identifierName);
            if (!_propertyName) {
              _propertyName = me.obfuscator.nameGen.generate();
              typeofMappings.set(identifierName, _propertyName);
            }
            unaryExpression.replaceWith(t.memberExpression(t.identifier(_objectName), t.stringLiteral(_propertyName), true));
            return;
          }
          var propertyName = mappings.get(identifierName);
          if (!propertyName) {
            propertyName = me.obfuscator.nameGen.generate();
            mappings.set(identifierName, propertyName);
          }

          // Only add setter if the identifier is modified
          if ((0, _astUtils.isModifiedIdentifier)(path)) {
            setterPropsNeeded.add(identifierName);
          }
          path.replaceWith(t.memberExpression(t.identifier(_objectName), t.stringLiteral(propertyName), true));
        }
      }
    },
    // Final AST handler
    // Very last step in the obfuscation process
    finalASTHandler: function finalASTHandler(ast) {
      if (me.obfuscator.parentObfuscator) return ast; // Only for root obfuscator

      // Create object expression
      // Very similar to flatten, maybe refactor to use the same code
      var objectProperties = [];
      me.changeData.globals = mappings.size;
      var _iterator = _createForOfIteratorHelper(mappings),
        _step;
      try {
        for (_iterator.s(); !(_step = _iterator.n()).done;) {
          var _step$value = _slicedToArray(_step.value, 2),
            identifierName = _step$value[0],
            propertyName = _step$value[1];
          // get identifier() { return identifier; }
          objectProperties.push(t.objectMethod("get", t.stringLiteral(propertyName), [], t.blockStatement([t.returnStatement(t.identifier(identifierName))])));

          // Only add setter if the identifier is modified
          if (setterPropsNeeded.has(identifierName)) {
            // set identifier(value) { return identifier = value; }
            objectProperties.push(t.objectMethod("set", t.stringLiteral(propertyName), [t.identifier(_objectName)], t.blockStatement([t.returnStatement(t.assignmentExpression("=", t.identifier(identifierName), t.identifier(_objectName)))])));
          }
        }

        // Add typeof mappings
      } catch (err) {
        _iterator.e(err);
      } finally {
        _iterator.f();
      }
      var _iterator2 = _createForOfIteratorHelper(typeofMappings),
        _step2;
      try {
        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
          var _step2$value = _slicedToArray(_step2.value, 2),
            _identifierName = _step2$value[0],
            _propertyName2 = _step2$value[1];
          // get typeof identifier() { return typeof identifier; }
          objectProperties.push(t.objectMethod("get", t.stringLiteral(_propertyName2), [], t.blockStatement([t.returnStatement(t.unaryExpression("typeof", t.identifier(_identifierName)))])));
        }
      } catch (err) {
        _iterator2.e(err);
      } finally {
        _iterator2.f();
      }
      var objectExpression = t.objectExpression(objectProperties);

      // Convert last expression to return statement
      // This preserves the last expression in the packed code
      var lastStatement = ast.program.body.at(-1);
      if (lastStatement && t.isExpressionStatement(lastStatement)) {
        Object.assign(lastStatement, t.returnStatement(lastStatement.expression));
      }
      var _outputCode = _obfuscator["default"].generateCode(ast, _objectSpread(_objectSpread({}, me.obfuscator.options), {}, {
        compact: true
      }));
      var newAST = new _template["default"]("\n    {prependNodes}\n    Function({objectName}, {outputCode})({objectExpression});\n  ").file({
        objectName: function objectName() {
          return t.stringLiteral(_objectName);
        },
        outputCode: function outputCode() {
          return t.stringLiteral(_outputCode);
        },
        objectExpression: objectExpression,
        prependNodes: prependNodes
      });
      return newAST;
    }
  };
}