"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var t = _interopRequireWildcard(require("@babel/types"));
var _order = require("../order");
var _constants = require("../constants");
var _assert = require("assert");
var _astUtils = require("../utils/ast-utils");
var _functionUtils = require("../utils/function-utils");
var _template = _interopRequireDefault(require("../templates/template"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { "default": e }; }
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { "default": e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n["default"] = e, t && t.set(e, n), n; }
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
/**
 * Preparation arranges the user's code into an AST the obfuscator can easily transform.
 *
 * ExplicitIdentifiers
 * - `object.IDENTIFIER` -> `object['IDENTIFIER']` // Now String Concealing can apply on it
 * - `{ IDENTIFIER: ... }` -> `{ "IDENTIFIER": ... }`
 *
 * ExplicitDeclarations
 * - `var a,b,c` -> `var a; var b; var c;` // Now Stack can apply on it
 *
 * Block
 * - `x => x * 2` -> `x => { return x * 2 }` // Change into Block Statements
 * - `if(true) return` -> `if (true) { return }`
 * - `while(a) a--;` -> `while(a) { a-- }`
 */
var _default = exports["default"] = function _default(_ref) {
  var Plugin = _ref.Plugin;
  var me = Plugin(_order.Order.Preparation);
  var markFunctionUnsafe = function markFunctionUnsafe(path) {
    var functionPath = path.findParent(function (path) {
      return path.isFunction() || path.isProgram();
    });
    if (!functionPath) return;
    var functionNode = functionPath.node;
    functionNode[_constants.UNSAFE] = true;
  };
  return {
    visitor: {
      "ThisExpression|Super": {
        exit: function exit(path) {
          markFunctionUnsafe(path);
        }
      },
      // @js-confuser-var "myVar" -> __JS_CONFUSER_VAR__(myVar)
      StringLiteral: {
        exit: function exit(path) {
          var _path$node$leadingCom;
          // Check for @js-confuser-var comment
          if ((_path$node$leadingCom = path.node.leadingComments) !== null && _path$node$leadingCom !== void 0 && _path$node$leadingCom.find(function (comment) {
            return comment.value.includes("@js-confuser-var");
          })) {
            var identifierName = path.node.value;
            (0, _assert.ok)(t.isValidIdentifier(identifierName), "Invalid identifier name: " + identifierName);

            // Create a new __JS_CONFUSER_VAR__ call with the identifier
            var newExpression = new _template["default"]("__JS_CONFUSER_VAR__({identifier})").expression({
              identifier: t.identifier(identifierName)
            });
            path.replaceWith(newExpression);

            // Remove comment and skip further processing
            path.node.leadingComments = [];
            path.skip();
          }
        }
      },
      // `Hello ${username}` -> "Hello " + username
      TemplateLiteral: {
        exit: function exit(path) {
          // Check if this is a tagged template literal, if yes, skip it
          if (t.isTaggedTemplateExpression(path.parent)) {
            return;
          }
          var _path$node = path.node,
            quasis = _path$node.quasis,
            expressions = _path$node.expressions;

          // Start with the first quasi (template string part)
          var binaryExpression = t.stringLiteral(quasis[0].value.cooked);

          // Loop over the remaining quasis and expressions, concatenating them
          for (var i = 0; i < expressions.length; i++) {
            // Add the expression as part of the binary concatenation
            binaryExpression = t.binaryExpression("+", binaryExpression, expressions[i]);

            // Add the next quasi (template string part)
            if (quasis[i + 1].value.cooked !== "") {
              binaryExpression = t.binaryExpression("+", binaryExpression, t.stringLiteral(quasis[i + 1].value.cooked));
            }
          }

          // Replace the template literal with the constructed binary expression
          path.replaceWith(binaryExpression);
        }
      },
      // /Hello World/g -> new RegExp("Hello World", "g")
      RegExpLiteral: {
        exit: function exit(path) {
          var _path$node2 = path.node,
            pattern = _path$node2.pattern,
            flags = _path$node2.flags;

          // Create a new RegExp() expression using the pattern and flags
          var newRegExpCall = t.newExpression(t.identifier("RegExp"),
          // Identifier for RegExp constructor
          [t.stringLiteral(pattern),
          // First argument: the pattern (no extra escaping needed)
          flags ? t.stringLiteral(flags) : t.stringLiteral("") // Second argument: the flags (if any)
          ]);

          // Replace the literal regex with the new RegExp() call
          path.replaceWith(newRegExpCall);
        }
      },
      ReferencedIdentifier: {
        exit: function exit(path) {
          var name = path.node.name;
          if (["arguments", "eval"].includes(name)) {
            markFunctionUnsafe(path);
          }

          // When Rename Variables is disabled, __JS_CONFUSER_VAR__ must still be removed
          if (!me.obfuscator.hasPlugin(_order.Order.RenameVariables) && (0, _functionUtils.isVariableFunctionIdentifier)(path)) {
            (0, _assert.ok)(path.parentPath.isCallExpression(), _constants.variableFunctionName + " must be directly called");
            var argument = path.parentPath.node.arguments[0];
            t.assertIdentifier(argument);

            // Remove the variableFunctionName call
            path.parentPath.replaceWith(t.stringLiteral(argument.name));
          }
        }
      },
      FunctionDeclaration: {
        exit: function exit(path) {
          // A function is 'predictable' if the parameter lengths are guaranteed to be known
          // a(true) -> predictable
          // (a || b)(true) -> unpredictable (Must be directly in a Call Expression)
          // a(...args) -> unpredictable (Cannot use SpreadElement)

          var name = path.node.id.name;
          var binding = path.scope.getBinding(name);
          var predictable = true;
          var maxArgLength = 0;
          var _iterator = _createForOfIteratorHelper(binding.referencePaths),
            _step;
          try {
            for (_iterator.s(); !(_step = _iterator.n()).done;) {
              var referencePath = _step.value;
              if (!referencePath.parentPath.isCallExpression()) {
                predictable = false;
                break;
              }
              var argsPath = referencePath.parentPath.get("arguments");
              var _iterator2 = _createForOfIteratorHelper(argsPath),
                _step2;
              try {
                for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
                  var arg = _step2.value;
                  if (arg.isSpreadElement()) {
                    predictable = false;
                    break;
                  }
                }
              } catch (err) {
                _iterator2.e(err);
              } finally {
                _iterator2.f();
              }
              if (argsPath.length > maxArgLength) {
                maxArgLength = argsPath.length;
              }
            }
          } catch (err) {
            _iterator.e(err);
          } finally {
            _iterator.f();
          }
          var definedArgLength = path.get("params").length;
          if (predictable && definedArgLength >= maxArgLength) {
            path.node[_constants.PREDICTABLE] = true;
          }
        }
      },
      // console.log() -> console["log"]();
      MemberExpression: {
        exit: function exit(path) {
          if (!path.node.computed && path.node.property.type === "Identifier") {
            path.node.property = t.stringLiteral(path.node.property.name);
            path.node.computed = true;
          }
        }
      },
      // { key: true } -> { "key": true }
      "Property|Method": {
        exit: function exit(_path) {
          var path = _path;
          if (t.isClassPrivateProperty(path.node)) return;
          if (!path.node.computed && path.node.key.type === "Identifier") {
            // Don't change constructor key
            if (t.isClassMethod(path.node) && path.node.kind === "constructor") return;
            path.node.key = t.stringLiteral(path.node.key.name);
            path.node.computed = true;
          }
        }
      },
      // var a,b,c -> var a; var b; var c;
      VariableDeclaration: {
        exit: function exit(path) {
          if (path.node.declarations.length > 1) {
            // E.g. for (var i = 0, j = 1;;)
            if (path.key === "init" && path.parentPath.isForStatement()) {
              if (!path.parentPath.node.test && !path.parentPath.node.update && path.node.kind === "var") {
                path.parentPath.insertBefore(path.node.declarations.map(function (declaration) {
                  return t.variableDeclaration(path.node.kind, [declaration]);
                }));
                path.remove();
              }
            } else {
              if (path.parentPath.isExportNamedDeclaration()) {
                path.parentPath.replaceWithMultiple(path.node.declarations.map(function (declaration) {
                  return t.exportNamedDeclaration(t.variableDeclaration(path.node.kind, [declaration]));
                }));
              } else {
                path.replaceWithMultiple(path.node.declarations.map(function (declaration, i) {
                  var names = Array.from((0, _astUtils.getPatternIdentifierNames)(path.get("declarations")[i]));
                  names.forEach(function (name) {
                    path.scope.removeBinding(name);
                  });
                  var newNode = t.variableDeclaration(path.node.kind, [declaration]);
                  return newNode;
                })).forEach(function (newPath) {
                  if (newPath.node.kind === "var") {
                    var functionOrProgram = (0, _astUtils.getParentFunctionOrProgram)(newPath);
                    functionOrProgram.scope.registerDeclaration(newPath);
                  }
                  newPath.scope.registerDeclaration(newPath);
                });
              }
            }
          }
        }
      },
      // () => a() -> () => { return a(); }
      ArrowFunctionExpression: {
        exit: function exit(path) {
          if (path.node.body.type !== "BlockStatement") {
            path.node.expression = false;
            path.node.body = t.blockStatement([t.returnStatement(path.node.body)]);
          }
        }
      },
      // if (a) b() -> if (a) { b(); }
      // if (a) {b()} else c() -> if (a) { b(); } else { c(); }
      IfStatement: {
        exit: function exit(path) {
          if (path.node.consequent.type !== "BlockStatement") {
            path.node.consequent = t.blockStatement([path.node.consequent]);
          }
          if (path.node.alternate && path.node.alternate.type !== "BlockStatement") {
            path.node.alternate = t.blockStatement([path.node.alternate]);
          }
        }
      },
      // for() d() -> for() { d(); }
      // while(a) b() -> while(a) { b(); }
      // with(a) b() -> with(a) { b(); }
      "ForStatement|ForInStatement|ForOfStatement|WhileStatement|WithStatement": {
        exit: function exit(_path) {
          var path = _path;
          if (path.node.body.type !== "BlockStatement") {
            path.node.body = t.blockStatement([path.node.body]);
          }
        }
      }
    }
  };
};