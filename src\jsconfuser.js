/**
 * JS-Confuser Integration Module
 * 
 * This module provides integration with the js-confuser library for JavaScript obfuscation.
 * It serves as the primary obfuscation engine in the processing pipeline.
 */

const { obfuscate } = require('js-confuser');
const { generateRandomName } = require('./utils');

/**
 * Default js-confuser configuration options
 */
const DEFAULT_JS_CONFUSER_OPTIONS = {
    hexadecimalNumbers: true,
    calculator: true,
    objectExtraction: true,
    astScrambler: true,
    preserveFunctionLength: true,
    minify: true,
    compact: true,
    renameVariables: true,
    renameLabels: true,
    renameGlobals: true,
    movedDeclarations: true,
    lock: {
        selfDefending: true
    }
};

/**
 * Process code using js-confuser obfuscation
 * @param {string} code - The JavaScript code to obfuscate
 * @param {Object} options - Optional configuration overrides
 * @returns {Object} Result object with obfuscated code and metadata
 */
async function processJsConfuser(code, options = {}) {
    try {
        // Extract target from global context if available
        const target = global.obfuscationTarget || options.target || 'browser';

        // Merge default options with provided options
        const jsConfuserOptions = {
            ...DEFAULT_JS_CONFUSER_OPTIONS,
            ...options,
            target: target,
            // Always use our custom identifier generator
            identifierGenerator: () => generateRandomName()
        };

        // Record start time for performance tracking
        const startTime = Date.now();

        // Perform js-confuser obfuscation
        const result = await obfuscate(code, jsConfuserOptions);

        // Calculate processing time
        const processingTime = Date.now() - startTime;

        // Validate result
        if (!result || !result.code) {
            throw new Error('JS-Confuser returned invalid result');
        }

        return {
            code: result.code,
            success: true,
            processingTime: processingTime,
            originalSize: Buffer.byteLength(code, 'utf8'),
            obfuscatedSize: Buffer.byteLength(result.code, 'utf8'),
            compressionRatio: Buffer.byteLength(result.code, 'utf8') / Buffer.byteLength(code, 'utf8'),
            transformsApplied: 'js-confuser-full-suite',
            metadata: {
                engine: 'js-confuser',
                version: '2.0.0',
                options: jsConfuserOptions
            }
        };

    } catch (error) {
        console.error('JS-Confuser processing error:', error);
        
        return {
            code: code, // Return original code on failure
            success: false,
            error: error.message,
            processingTime: 0,
            originalSize: Buffer.byteLength(code, 'utf8'),
            obfuscatedSize: Buffer.byteLength(code, 'utf8'),
            compressionRatio: 1.0,
            transformsApplied: 'none',
            metadata: {
                engine: 'js-confuser',
                version: '2.0.0',
                error: error.message
            }
        };
    }
}

/**
 * Get default js-confuser options
 * @returns {Object} Default configuration object
 */
function getDefaultJsConfuserOptions() {
    return { ...DEFAULT_JS_CONFUSER_OPTIONS };
}

/**
 * Create custom js-confuser options for specific targets
 * @param {string} target - Target environment ('browser', 'node')
 * @param {Object} customOptions - Additional options to merge
 * @returns {Object} Configured options object
 */
function createJsConfuserOptions(target = 'browser', customOptions = {}) {
    const baseOptions = {
        ...DEFAULT_JS_CONFUSER_OPTIONS,
        target: target
    };

    // Target-specific optimizations
    if (target === 'node') {
        baseOptions.globalConcealing = true;
        baseOptions.lock.selfDefending = false; // Less aggressive for Node.js
    } else if (target === 'browser') {
        baseOptions.globalConcealing = false;
        baseOptions.lock.selfDefending = true; // More aggressive for browser
    }

    return {
        ...baseOptions,
        ...customOptions
    };
}

module.exports = {
    processJsConfuser,
    getDefaultJsConfuserOptions,
    createJsConfuserOptions,
    DEFAULT_JS_CONFUSER_OPTIONS
};
