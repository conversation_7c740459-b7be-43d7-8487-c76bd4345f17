"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var t = _interopRequireWildcard(require("@babel/types"));
var _assert = require("assert");
var _order = require("../order");
var _constants = require("../constants");
var _template = _interopRequireDefault(require("../templates/template"));
var _astUtils = require("../utils/ast-utils");
function _interopRequireDefault(e) { return e && e.__esModule ? e : { "default": e }; }
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { "default": e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n["default"] = e, t && t.set(e, n), n; }
function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _iterableToArray(r) { if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r); }
function _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
var _default = exports["default"] = function _default(_ref) {
  var Plugin = _ref.Plugin;
  var me = Plugin(_order.Order.AstScrambler, {
    changeData: {
      expressions: 0
    }
  });
  var callExprName;
  return {
    visitor: {
      "Block|SwitchCase": {
        exit: function exit(_path) {
          var path = _path;
          var isProgram = path.isProgram();
          var containerKey;
          if (path.isSwitchCase()) {
            containerKey = "consequent";
          } else if (path.isBlock()) {
            containerKey = "body";
          }
          var container = path.node[containerKey];
          var newContainer = [];
          (0, _assert.ok)(Array.isArray(container));
          var expressions = [];
          var flushExpressions = function flushExpressions() {
            if (!expressions.length) return;

            // Not enough expressions to require a call expression
            if (expressions.length === 1) {
              newContainer.push(t.expressionStatement(expressions[0]));
              expressions = [];
              return;
            }
            if (!callExprName) {
              callExprName = me.getPlaceholder() + "_ast";
            }
            me.changeData.expressions += expressions.length;
            newContainer.push(t.expressionStatement(t.callExpression(t.identifier(callExprName), expressions)));
            expressions = [];
          };
          var _iterator = _createForOfIteratorHelper(container),
            _step;
          try {
            for (_iterator.s(); !(_step = _iterator.n()).done;) {
              var statement = _step.value;
              if (
              // Preserve last expression at the top level
              (isProgram ? statement !== container.at(-1) : true) && t.isExpressionStatement(statement) && !statement[_constants.SKIP]) {
                if (t.isSequenceExpression(statement.expression)) {
                  var _expressions;
                  (_expressions = expressions).push.apply(_expressions, _toConsumableArray(statement.expression.expressions));
                } else {
                  expressions.push(statement.expression);
                }
              } else {
                flushExpressions();
                newContainer.push(statement);
              }
            }
          } catch (err) {
            _iterator.e(err);
          } finally {
            _iterator.f();
          }
          flushExpressions();
          path.node[containerKey] = newContainer;
          if (path.isProgram()) {
            if (callExprName) {
              var functionDeclaration = new _template["default"]("\n                function ".concat(callExprName, "(){\n                  ").concat(callExprName, " = function(){};\n                }\n                ")).single();
              (0, _astUtils.append)(path, functionDeclaration);
            }
          }
        }
      }
    }
  };
};