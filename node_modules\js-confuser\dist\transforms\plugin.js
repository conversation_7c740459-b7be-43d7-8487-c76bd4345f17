"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.PluginInstance = void 0;
var _randomUtils = require("../utils/random-utils");
var t = _interopRequireWildcard(require("@babel/types"));
var _constants = require("../constants");
var _setFunctionLengthTemplate = require("../templates/setFunctionLengthTemplate");
var _astUtils = require("../utils/ast-utils");
var _node = require("../utils/node");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { "default": e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n["default"] = e, t && t.set(e, n), n; }
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var PluginInstance = exports.PluginInstance = /*#__PURE__*/function () {
  function PluginInstance(pluginOptions, obfuscator) {
    _classCallCheck(this, PluginInstance);
    _defineProperty(this, "changeData", {});
    this.pluginOptions = pluginOptions;
    this.obfuscator = obfuscator;
    this.computeProbabilityMap = obfuscator.computeProbabilityMap.bind(this.obfuscator);
  }
  return _createClass(PluginInstance, [{
    key: "name",
    get: function get() {
      return this.pluginOptions.name || "unnamed";
    }
  }, {
    key: "order",
    get: function get() {
      return this.pluginOptions.order;
    }
  }, {
    key: "options",
    get: function get() {
      return this.obfuscator.options;
    }
  }, {
    key: "globalState",
    get: function get() {
      return this.obfuscator.globalState;
    }
  }, {
    key: "skip",
    value: function skip(path) {
      var _this = this;
      if (Array.isArray(path)) {
        path.forEach(function (p) {
          return _this.skip(p);
        });
      } else {
        var any = path;
        var node = any.isNodeType ? any.node : any;
        node[_constants.SKIP] = this.order;
        return node;
      }
    }

    /**
     * Returns `true` if the given path has been skipped by this plugin.
     */
  }, {
    key: "isSkipped",
    value: function isSkipped(path) {
      return path.node[_constants.SKIP] === this.order;
    }
  }, {
    key: "setFunctionLength",
    value: function setFunctionLength(path, originalLength) {
      path.node[_constants.FN_LENGTH] = originalLength;

      // Skip if user disabled this feature
      if (!this.options.preserveFunctionLength) return;

      // Skip if function has no parameters
      if (originalLength === 0) return;

      // Create the function length setter if it doesn't exist
      if (!this.setFunctionLengthName) {
        this.setFunctionLengthName = this.getPlaceholder("fnLength");
        this.skip((0, _astUtils.prependProgram)(path, _setFunctionLengthTemplate.SetFunctionLengthTemplate.compile({
          fnName: this.setFunctionLengthName
        })));
      }
      var createCallArguments = function createCallArguments(node) {
        var args = [node];

        // 1 is the default value in the setFunction template, can exclude it
        if (originalLength !== 1) {
          args.push((0, _node.numericLiteral)(originalLength));
        }
        return args;
      };
      if (t.isFunctionDeclaration(path.node)) {
        (0, _astUtils.prepend)(path.parentPath, t.expressionStatement(t.callExpression(t.identifier(this.setFunctionLengthName), createCallArguments(t.identifier(path.node.id.name)))));
      } else if (t.isFunctionExpression(path.node) || t.isArrowFunctionExpression(path.node)) {
        path.replaceWith(t.callExpression(t.identifier(this.setFunctionLengthName), createCallArguments(path.node)));
      } else {
        // TODO
      }
    }

    /**
     * Returns a random string.
     *
     * Used for creating temporary variables names, typically before RenameVariables has ran.
     *
     * These long temp names will be converted to short, mangled names by RenameVariables.
     */
  }, {
    key: "getPlaceholder",
    value: function getPlaceholder() {
      var suffix = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : "";
      return "__p_" + (0, _randomUtils.getRandomString)(4) + (suffix ? "_" + suffix : "");
    }

    /**
     * Logs a message to the console, only if `verbose` is enabled.
     * @param messages
     */
  }, {
    key: "log",
    value: function log() {
      if (this.options.verbose) {
        var _console;
        for (var _len = arguments.length, messages = new Array(_len), _key = 0; _key < _len; _key++) {
          messages[_key] = arguments[_key];
        }
        (_console = console).log.apply(_console, ["[".concat(this.name, "]")].concat(messages));
      }
    }

    /**
     * Logs a warning to the console, only if `verbose` is enabled.
     * @param messages
     */
  }, {
    key: "warn",
    value: function warn() {
      if (this.options.verbose) {
        var _console2;
        for (var _len2 = arguments.length, messages = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
          messages[_key2] = arguments[_key2];
        }
        (_console2 = console).log.apply(_console2, ["WARN [".concat(this.name, "]")].concat(messages));
      }
    }

    /**
     * Throws an error with the given message.
     * @param messages
     */
  }, {
    key: "error",
    value: function error() {
      for (var _len3 = arguments.length, messages = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
        messages[_key3] = arguments[_key3];
      }
      throw new Error("[".concat(this.name, "] ").concat(messages.join(" ")));
    }
  }]);
}();