"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.createDefaultStringEncoding = createDefaultStringEncoding;
var _template = _interopRequireDefault(require("../../templates/template"));
var _randomUtils = require("../../utils/random-utils");
var t = _interopRequireWildcard(require("@babel/types"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { "default": e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n["default"] = e, t && t.set(e, n), n; }
function _interopRequireDefault(e) { return e && e.__esModule ? e : { "default": e }; }
var hasAllEncodings = false;
function createDefaultStringEncoding(encodingImplementations) {
  if (hasAllEncodings) {
    return null;
  }

  // Create base91 encoding
  var strTable = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!#$%&()*+,./:;<=>?@[]^_`{|}~"';

  // Randomize the charset
  strTable = (0, _randomUtils.shuffle)(strTable.split("")).join("");
  var identity = "base91_" + strTable;

  // Check if the encoding already exists
  if (typeof encodingImplementations[identity] !== "undefined") {
    hasAllEncodings = true;
    return null;
  }
  var encodingImplementation = {
    identity: identity,
    encode: function encode(str) {
      var table = strTable;
      var raw = Buffer.from(str, "utf-8");
      var len = raw.length;
      var ret = "";
      var n = 0;
      var b = 0;
      for (var i = 0; i < len; i++) {
        b |= raw[i] << n;
        n += 8;
        if (n > 13) {
          var v = b & 8191;
          if (v > 88) {
            b >>= 13;
            n -= 13;
          } else {
            v = b & 16383;
            b >>= 14;
            n -= 14;
          }
          ret += table[v % 91] + table[v / 91 | 0];
        }
      }
      if (n) {
        ret += table[b % 91];
        if (n > 7 || b > 90) ret += table[b / 91 | 0];
      }
      return ret;
    },
    decode: function decode(str) {
      var table = strTable;
      var raw = "" + (str || "");
      var len = raw.length;
      var ret = [];
      var b = 0;
      var n = 0;
      var v = -1;
      for (var i = 0; i < len; i++) {
        var p = table.indexOf(raw[i]);
        if (p === -1) continue;
        if (v < 0) {
          v = p;
        } else {
          v += p * 91;
          b |= v << n;
          n += (v & 8191) > 88 ? 13 : 14;
          do {
            ret.push(b & 0xff);
            b >>= 8;
            n -= 8;
          } while (n > 7);
          v = -1;
        }
      }
      if (v > -1) {
        ret.push((b | v << n) & 0xff);
      }
      return Buffer.from(ret).toString("utf-8");
    },
    code: new _template["default"]("  \n        function {fnName}(str){\n          var table = {__strTable__};\n  \n          var raw = \"\" + (str || \"\");\n          var len = raw.length;\n          var ret = [];\n  \n          var b = 0;\n          var n = 0;\n          var v = -1;\n  \n          for (var i = 0; i < len; i++) {\n            var p = table.indexOf(raw[i]);\n            if (p === -1) continue;\n            if (v < 0) {\n              v = p;\n            } else {\n              v += p * 91;\n              b |= v << n;\n              n += (v & 8191) > 88 ? 13 : 14;\n              do {\n                ret.push(b & 0xff);\n                b >>= 8;\n                n -= 8;\n              } while (n > 7);\n              v = -1;\n            }\n          }\n  \n          if (v > -1) {\n            ret.push((b | (v << n)) & 0xff);\n          }\n  \n          return {__bufferToStringFunction__}(ret);\n        }\n      ").setDefaultVariables({
      __strTable__: t.stringLiteral(strTable)
    })
  };
  return encodingImplementation;
}