"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var t = _interopRequireWildcard(require("@babel/types"));
var _order = require("../order");
var _stringEncoding = _interopRequireDefault(require("./string/stringEncoding"));
var _constants = require("../constants");
var _assert = require("assert");
function _interopRequireDefault(e) { return e && e.__esModule ? e : { "default": e }; }
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { "default": e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n["default"] = e, t && t.set(e, n), n; }
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var _default = exports["default"] = function _default(_ref) {
  var Plugin = _ref.Plugin;
  var me = Plugin(_order.Order.Finalizer);
  var stringEncodingPlugin = (0, _stringEncoding["default"])(me);
  return {
    visitor: _objectSpread(_objectSpread(_objectSpread({}, stringEncodingPlugin.visitor), me.obfuscator.hasPlugin(_order.Order.RenameVariables) ? {} : {
      CallExpression: {
        exit: function exit(path) {
          if (path.get("callee").isIdentifier({
            name: _constants.variableFunctionName
          })) {
            var args = path.get("arguments");
            (0, _assert.ok)(args.length === 1);
            var arg = args[0];
            (0, _assert.ok)(arg.isIdentifier());
            var name = arg.node.name;
            path.replaceWith(t.stringLiteral(name));
          }
        }
      }
    }), {}, {
      // Hexadecimal numbers
      NumericLiteral: {
        exit: function exit(path) {
          if (me.options.hexadecimalNumbers) {
            var value = path.node.value;
            if (Number.isNaN(value) || !Number.isFinite(value) || Math.floor(value) !== value) {
              return;
            }

            // Technically, a Literal will never be negative because it's supposed to be inside a UnaryExpression with a "-" operator.
            // This code handles it regardless
            var isNegative = value < 0;
            var hex = Math.abs(value).toString(16);
            var newStr = (isNegative ? "-" : "") + "0x" + hex;
            var id = t.identifier(newStr);
            id[_constants.GEN_NODE] = true;
            path.replaceWith(id);
            path.skip();
          }
        }
      }
    })
  };
};