"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var t = _interopRequireWildcard(require("@babel/types"));
var _order = require("../../order");
var _astUtils = require("../../utils/ast-utils");
var _node = require("../../utils/node");
var _stringCompressionTemplate = require("../../templates/stringCompressionTemplate");
var _obfuscator = _interopRequireDefault(require("../../obfuscator"));
var _getGlobalTemplate = require("../../templates/getGlobalTemplate");
var _constants = require("../../constants");
function _interopRequireDefault(e) { return e && e.__esModule ? e : { "default": e }; }
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { "default": e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n["default"] = e, t && t.set(e, n), n; }
var LZString = require("lz-string");
var _default = exports["default"] = function _default(_ref) {
  var Plugin = _ref.Plugin;
  var me = Plugin(_order.Order.StringCompression, {
    changeData: {
      strings: 0
    }
  });

  // String Compression is only applied to the main obfuscator
  // Any RGF functions will not have string compression due to the size of the decompression function

  var _stringDelimiter = "|";
  return {
    visitor: {
      Program: {
        exit: function exit(programPath) {
          var stringFn = me.getPlaceholder() + "_SC";
          var stringMap = new Map();

          // Find all the strings
          programPath.traverse({
            StringLiteral: {
              exit: function exit(path) {
                // Don't change module imports
                if ((0, _astUtils.isModuleImport)(path)) return;
                var originalValue = path.node.value;

                // Must be at least 3 characters long
                if (originalValue.length < 3) return;

                // Cannot contain the string delimiter
                if (originalValue.includes(_stringDelimiter)) return;
                var index = stringMap.get(originalValue);
                if (typeof index === "undefined") {
                  // Allow user option to skip compression for certain strings
                  if (!me.computeProbabilityMap(me.options.stringCompression, originalValue)) {
                    return;
                  }
                  index = stringMap.size;
                  stringMap.set(originalValue, index);
                }
                me.changeData.strings++;
                (0, _astUtils.ensureComputedExpression)(path);
                path.replaceWith(t.callExpression(t.identifier(stringFn), [(0, _node.numericLiteral)(index)]));
              }
            }
          });

          // No strings changed
          if (stringMap.size === 0) return;
          var stringPayload = Array.from(stringMap.keys()).join(_stringDelimiter);

          // Compress the string
          var compressedString = LZString.compressToUTF16(stringPayload);
          var stringCompressionLibraryName = me.obfuscator.getStringCompressionLibraryName();
          var insertStringCompressionLibrary = !me.obfuscator.parentObfuscator;
          (0, _astUtils.prependProgram)(programPath, _stringCompressionTemplate.StringCompressionTemplate.compile({
            stringFn: stringFn,
            stringName: me.getPlaceholder(),
            stringArray: me.getPlaceholder(),
            stringDelimiter: function stringDelimiter() {
              return t.stringLiteral(_stringDelimiter);
            },
            stringValue: function stringValue() {
              return t.stringLiteral(compressedString);
            },
            GetGlobalTemplate: (0, _getGlobalTemplate.createGetGlobalTemplate)(me, programPath),
            getGlobalFnName: me.getPlaceholder(),
            StringCompressionLibrary: stringCompressionLibraryName
          }));
          if (insertStringCompressionLibrary) {
            // RGF functions should not clone the entire decompression function
            (0, _astUtils.prependProgram)(programPath, _obfuscator["default"].parseCode(_stringCompressionTemplate.StringCompressionLibraryMinified.replace(/{StringCompressionLibrary}/g, stringCompressionLibraryName)).program.body)[0].get("declarations")[0].get("id").node[_constants.NO_RENAME] = true;
          }
        }
      }
    }
  };
};